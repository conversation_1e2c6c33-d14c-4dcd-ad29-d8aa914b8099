{"id": "shell", "name": "shell", "metaData": {"name": "shell", "type": "app", "buildInfo": {"buildVersion": "1.0.0", "buildName": "@crm/shell"}, "remoteEntry": {"name": "", "path": "", "type": "global"}, "types": {"path": "", "name": "", "zip": ".", "api": "."}, "globalName": "shell", "pluginVersion": "0.6.16", "publicPath": "auto", "prefetchInterface": false}, "shared": [{"id": "shell:react-dom/client", "name": "react-dom/client", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}}, {"id": "shell:react-dom", "name": "react-dom", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}}, {"id": "shell:react/jsx-runtime", "name": "react/jsx-runtime", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}}, {"id": "shell:react", "name": "react", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}}], "remotes": [{"federationContainerName": "transcriptAndSummary", "moduleName": "UNKNOWN", "alias": "transcriptAndSummary", "entry": "http://localhost:5176/remoteEntry.js"}, {"federationContainerName": "ifPartyMaster", "moduleName": "UNKNOWN", "alias": "ifPartyMaster", "entry": "http://localhost:5174/remoteEntry.js"}], "exposes": []}