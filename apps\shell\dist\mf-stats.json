{"id": "shell", "name": "shell", "metaData": {"name": "shell", "type": "app", "buildInfo": {"buildVersion": "1.0.0", "buildName": "@crm/shell"}, "remoteEntry": {"name": "", "path": "", "type": "global"}, "types": {"path": "", "name": "", "zip": ".", "api": "."}, "globalName": "shell", "pluginVersion": "0.6.16", "publicPath": "auto"}, "shared": [{"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react-dom/client", "version": "18.3.1", "eager": false, "id": "shell:react-dom/client", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react-dom", "version": "18.3.1", "eager": false, "id": "shell:react-dom", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react/jsx-runtime", "version": "18.3.1", "eager": false, "id": "shell:react/jsx-runtime", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react", "version": "18.3.1", "eager": false, "id": "shell:react", "assets": {"js": {"async": ["974.js", "567.js"], "sync": ["main.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}], "remotes": [{"entry": "http://localhost:5176/remoteEntry.js", "alias": "transcriptAndSummary", "moduleName": "UNKNOWN", "federationContainerName": "transcriptAndSummary", "consumingFederationContainerName": "shell", "usedIn": []}, {"entry": "http://localhost:5174/remoteEntry.js", "alias": "ifPartyMaster", "moduleName": "UNKNOWN", "federationContainerName": "ifPartyMaster", "consumingFederationContainerName": "shell", "usedIn": []}], "exposes": []}