{"id": "ifPartyMaster", "name": "ifPartyMaster", "metaData": {"name": "ifPartyMaster", "type": "app", "buildInfo": {"buildVersion": "1.0.0", "buildName": "@crm/if-party-master"}, "remoteEntry": {"name": "remoteEntry.js", "path": "", "type": "global"}, "types": {"path": "", "name": "", "zip": "", "api": ""}, "globalName": "ifPartyMaster", "pluginVersion": "0.6.16", "publicPath": "auto"}, "shared": [{"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react-dom/client", "version": "18.3.1", "eager": false, "id": "ifPartyMaster:react-dom/client", "assets": {"js": {"async": ["144.js", "41.js"], "sync": ["873.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react-dom", "version": "18.3.1", "eager": false, "id": "ifPartyMaster:react-dom", "assets": {"js": {"async": ["41.js"], "sync": ["144.js"]}, "css": {"async": [], "sync": []}}, "usedIn": []}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react/jsx-runtime", "version": "18.3.1", "eager": false, "id": "ifPartyMaster:react/jsx-runtime", "assets": {"js": {"async": ["41.js"], "sync": ["85.js", "466.js"]}, "css": {"async": [], "sync": []}}, "usedIn": ["./App"]}, {"singleton": true, "requiredVersion": "^18.3.1", "shareScope": "default", "name": "react", "version": "18.3.1", "eager": false, "id": "ifPartyMaster:react", "assets": {"js": {"async": [], "sync": ["41.js"]}, "css": {"async": [], "sync": []}}, "usedIn": ["./App"]}], "remotes": [], "exposes": [{"path": "./App", "id": "ifPartyMaster:App", "name": "App", "requires": ["react/jsx-runtime", "react"], "file": "src\\App.tsx", "assets": {"js": {"sync": ["__federation_expose_App.js"], "async": ["466.js", "41.js", "567.js"]}, "css": {"sync": [], "async": []}}}]}