/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 391:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3091);\n/* harmony import */ var C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\nif(!__webpack_require__.federation.runtime){\n\tvar prevFederation = __webpack_require__.federation;\n\t__webpack_require__.federation = {}\n\tfor(var key in (C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default())){\n\t\t__webpack_require__.federation[key] = (C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default())[key];\n\t}\n\tfor(var key in prevFederation){\n\t\t__webpack_require__.federation[key] = prevFederation[key];\n\t}\n}\nif(!__webpack_require__.federation.instance){\n\n\t__webpack_require__.federation.instance = __webpack_require__.federation.runtime.init(__webpack_require__.federation.initOptions);\n\tif(__webpack_require__.federation.attachShareScopeMap){\n\t\t__webpack_require__.federation.attachShareScopeMap(__webpack_require__)\n\t}\n\tif(__webpack_require__.federation.installInitialConsumes){\n\t\t__webpack_require__.federation.installInitialConsumes()\n\t}\n\n\tif(!__webpack_require__.federation.isMFRemote && __webpack_require__.federation.prefetch){\n\t__webpack_require__.federation.prefetch()\n\t}\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///391\n\n}");

/***/ }),

/***/ 1784:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nexports._extends = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTc4NC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQkFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL3RyYW5zY3JpcHQtYW5kLXN1bW1hcnkvLi4vLi4vbm9kZV9tb2R1bGVzL0Btb2R1bGUtZmVkZXJhdGlvbi9zZGsvZGlzdC9wb2x5ZmlsbHMuY2pzLmpzPzI2NWQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgICBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gYXNzaWduKHRhcmdldCkge1xuICAgICAgICBmb3IodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAgICAgIHZhciBzb3VyY2UgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IodmFyIGtleSBpbiBzb3VyY2UpaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzb3VyY2UsIGtleSkpIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRhcmdldDtcbiAgICB9O1xuICAgIHJldHVybiBfZXh0ZW5kcy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuXG5leHBvcnRzLl9leHRlbmRzID0gX2V4dGVuZHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///1784\n\n}");

/***/ }),

/***/ 2214:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar polyfills = __webpack_require__(6689);\nvar sdk = __webpack_require__(9468);\n\nfunction getBuilderId() {\n    //@ts-ignore\n    return  true ? \"transcriptAndSummary:1.0.0\" : 0;\n}\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// FIXME: pre-bundle ?\nconst logger = sdk.createLogger(LOG_CATEGORY);\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction assert(condition, msg) {\n    if (!condition) {\n        error(msg);\n    }\n}\nfunction error(msg) {\n    if (msg instanceof Error) {\n        msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        throw msg;\n    }\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n}\nfunction warn(msg) {\n    if (msg instanceof Error) {\n        msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        logger.warn(msg);\n    } else {\n        logger.warn(msg);\n    }\n}\n\nfunction addUniqueItem(arr, item) {\n    if (arr.findIndex((name)=>name === item) === -1) {\n        arr.push(item);\n    }\n    return arr;\n}\nfunction getFMId(remoteInfo) {\n    if ('version' in remoteInfo && remoteInfo.version) {\n        return `${remoteInfo.name}:${remoteInfo.version}`;\n    } else if ('entry' in remoteInfo && remoteInfo.entry) {\n        return `${remoteInfo.name}:${remoteInfo.entry}`;\n    } else {\n        return `${remoteInfo.name}`;\n    }\n}\nfunction isRemoteInfoWithEntry(remote) {\n    return typeof remote.entry !== 'undefined';\n}\nfunction isPureRemoteEntry(remote) {\n    return !remote.entry.includes('.json') && remote.entry.includes('.js');\n}\nfunction isObject(val) {\n    return val && typeof val === 'object';\n}\nconst objectToString = Object.prototype.toString;\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isPlainObject(val) {\n    return objectToString.call(val) === '[object Object]';\n}\nfunction arrayOptions(options) {\n    return Array.isArray(options) ? options : [\n        options\n    ];\n}\nfunction getRemoteEntryInfoFromSnapshot(snapshot) {\n    const defaultRemoteEntryInfo = {\n        url: '',\n        type: 'global',\n        globalName: ''\n    };\n    if (sdk.isBrowserEnv()) {\n        return 'remoteEntry' in snapshot ? {\n            url: snapshot.remoteEntry,\n            type: snapshot.remoteEntryType,\n            globalName: snapshot.globalName\n        } : defaultRemoteEntryInfo;\n    }\n    if ('ssrRemoteEntry' in snapshot) {\n        return {\n            url: snapshot.ssrRemoteEntry || defaultRemoteEntryInfo.url,\n            type: snapshot.ssrRemoteEntryType || defaultRemoteEntryInfo.type,\n            globalName: snapshot.globalName\n        };\n    }\n    return defaultRemoteEntryInfo;\n}\n\nconst nativeGlobal = (()=>{\n    try {\n        return new Function('return this')();\n    } catch (e) {\n        return globalThis;\n    }\n})();\nconst Global = nativeGlobal;\nfunction definePropertyGlobalVal(target, key, val) {\n    Object.defineProperty(target, key, {\n        value: val,\n        configurable: false,\n        writable: true\n    });\n}\nfunction includeOwnProperty(target, key) {\n    return Object.hasOwnProperty.call(target, key);\n}\n// This section is to prevent encapsulation by certain microfrontend frameworks. Due to reuse policies, sandbox escapes.\n// The sandbox in the microfrontend does not replicate the value of 'configurable'.\n// If there is no loading content on the global object, this section defines the loading object.\nif (!includeOwnProperty(globalThis, '__GLOBAL_LOADING_REMOTE_ENTRY__')) {\n    definePropertyGlobalVal(globalThis, '__GLOBAL_LOADING_REMOTE_ENTRY__', {});\n}\nconst globalLoading = globalThis.__GLOBAL_LOADING_REMOTE_ENTRY__;\nfunction setGlobalDefaultVal(target) {\n    var _target___FEDERATION__, _target___FEDERATION__1, _target___FEDERATION__2, _target___FEDERATION__3, _target___FEDERATION__4, _target___FEDERATION__5;\n    if (includeOwnProperty(target, '__VMOK__') && !includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', target.__VMOK__);\n    }\n    if (!includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', {\n            __GLOBAL_PLUGIN__: [],\n            __INSTANCES__: [],\n            moduleInfo: {},\n            __SHARE__: {},\n            __MANIFEST_LOADING__: {},\n            __PRELOADED_MAP__: new Map()\n        });\n        definePropertyGlobalVal(target, '__VMOK__', target.__FEDERATION__);\n    }\n    var ___GLOBAL_PLUGIN__;\n    (___GLOBAL_PLUGIN__ = (_target___FEDERATION__ = target.__FEDERATION__).__GLOBAL_PLUGIN__) != null ? ___GLOBAL_PLUGIN__ : _target___FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    var ___INSTANCES__;\n    (___INSTANCES__ = (_target___FEDERATION__1 = target.__FEDERATION__).__INSTANCES__) != null ? ___INSTANCES__ : _target___FEDERATION__1.__INSTANCES__ = [];\n    var _moduleInfo;\n    (_moduleInfo = (_target___FEDERATION__2 = target.__FEDERATION__).moduleInfo) != null ? _moduleInfo : _target___FEDERATION__2.moduleInfo = {};\n    var ___SHARE__;\n    (___SHARE__ = (_target___FEDERATION__3 = target.__FEDERATION__).__SHARE__) != null ? ___SHARE__ : _target___FEDERATION__3.__SHARE__ = {};\n    var ___MANIFEST_LOADING__;\n    (___MANIFEST_LOADING__ = (_target___FEDERATION__4 = target.__FEDERATION__).__MANIFEST_LOADING__) != null ? ___MANIFEST_LOADING__ : _target___FEDERATION__4.__MANIFEST_LOADING__ = {};\n    var ___PRELOADED_MAP__;\n    (___PRELOADED_MAP__ = (_target___FEDERATION__5 = target.__FEDERATION__).__PRELOADED_MAP__) != null ? ___PRELOADED_MAP__ : _target___FEDERATION__5.__PRELOADED_MAP__ = new Map();\n}\nsetGlobalDefaultVal(globalThis);\nsetGlobalDefaultVal(nativeGlobal);\nfunction resetFederationGlobalInfo() {\n    globalThis.__FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    globalThis.__FEDERATION__.__INSTANCES__ = [];\n    globalThis.__FEDERATION__.moduleInfo = {};\n    globalThis.__FEDERATION__.__SHARE__ = {};\n    globalThis.__FEDERATION__.__MANIFEST_LOADING__ = {};\n}\nfunction getGlobalFederationInstance(name, version) {\n    const buildId = getBuilderId();\n    return globalThis.__FEDERATION__.__INSTANCES__.find((GMInstance)=>{\n        if (buildId && GMInstance.options.id === getBuilderId()) {\n            return true;\n        }\n        if (GMInstance.options.name === name && !GMInstance.options.version && !version) {\n            return true;\n        }\n        if (GMInstance.options.name === name && version && GMInstance.options.version === version) {\n            return true;\n        }\n        return false;\n    });\n}\nfunction setGlobalFederationInstance(FederationInstance) {\n    globalThis.__FEDERATION__.__INSTANCES__.push(FederationInstance);\n}\nfunction getGlobalFederationConstructor() {\n    return globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR__;\n}\nfunction setGlobalFederationConstructor(FederationConstructor, isDebug = sdk.isDebugMode()) {\n    if (isDebug) {\n        globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR__ = FederationConstructor;\n        globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR_VERSION__ = \"0.6.16\";\n    }\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction getInfoWithoutType(target, key) {\n    if (typeof key === 'string') {\n        const keyRes = target[key];\n        if (keyRes) {\n            return {\n                value: target[key],\n                key: key\n            };\n        } else {\n            const targetKeys = Object.keys(target);\n            for (const targetKey of targetKeys){\n                const [targetTypeOrName, _] = targetKey.split(':');\n                const nKey = `${targetTypeOrName}:${key}`;\n                const typeWithKeyRes = target[nKey];\n                if (typeWithKeyRes) {\n                    return {\n                        value: typeWithKeyRes,\n                        key: nKey\n                    };\n                }\n            }\n            return {\n                value: undefined,\n                key: key\n            };\n        }\n    } else {\n        throw new Error('key must be string');\n    }\n}\nconst getGlobalSnapshot = ()=>nativeGlobal.__FEDERATION__.moduleInfo;\nconst getTargetSnapshotInfoByModuleInfo = (moduleInfo, snapshot)=>{\n    // Check if the remote is included in the hostSnapshot\n    const moduleKey = getFMId(moduleInfo);\n    const getModuleInfo = getInfoWithoutType(snapshot, moduleKey).value;\n    // The remoteSnapshot might not include a version\n    if (getModuleInfo && !getModuleInfo.version && 'version' in moduleInfo && moduleInfo['version']) {\n        getModuleInfo.version = moduleInfo['version'];\n    }\n    if (getModuleInfo) {\n        return getModuleInfo;\n    }\n    // If the remote is not included in the hostSnapshot, deploy a micro app snapshot\n    if ('version' in moduleInfo && moduleInfo['version']) {\n        const { version } = moduleInfo, resModuleInfo = polyfills._object_without_properties_loose(moduleInfo, [\n            \"version\"\n        ]);\n        const moduleKeyWithoutVersion = getFMId(resModuleInfo);\n        const getModuleInfoWithoutVersion = getInfoWithoutType(nativeGlobal.__FEDERATION__.moduleInfo, moduleKeyWithoutVersion).value;\n        if ((getModuleInfoWithoutVersion == null ? void 0 : getModuleInfoWithoutVersion.version) === version) {\n            return getModuleInfoWithoutVersion;\n        }\n    }\n    return;\n};\nconst getGlobalSnapshotInfoByModuleInfo = (moduleInfo)=>getTargetSnapshotInfoByModuleInfo(moduleInfo, nativeGlobal.__FEDERATION__.moduleInfo);\nconst setGlobalSnapshotInfoByModuleInfo = (remoteInfo, moduleDetailInfo)=>{\n    const moduleKey = getFMId(remoteInfo);\n    nativeGlobal.__FEDERATION__.moduleInfo[moduleKey] = moduleDetailInfo;\n    return nativeGlobal.__FEDERATION__.moduleInfo;\n};\nconst addGlobalSnapshot = (moduleInfos)=>{\n    nativeGlobal.__FEDERATION__.moduleInfo = polyfills._extends({}, nativeGlobal.__FEDERATION__.moduleInfo, moduleInfos);\n    return ()=>{\n        const keys = Object.keys(moduleInfos);\n        for (const key of keys){\n            delete nativeGlobal.__FEDERATION__.moduleInfo[key];\n        }\n    };\n};\nconst getRemoteEntryExports = (name, globalName)=>{\n    const remoteEntryKey = globalName || `__FEDERATION_${name}:custom__`;\n    const entryExports = globalThis[remoteEntryKey];\n    return {\n        remoteEntryKey,\n        entryExports\n    };\n};\n// This function is used to register global plugins.\n// It iterates over the provided plugins and checks if they are already registered.\n// If a plugin is not registered, it is added to the global plugins.\n// If a plugin is already registered, a warning message is logged.\nconst registerGlobalPlugins = (plugins)=>{\n    const { __GLOBAL_PLUGIN__ } = nativeGlobal.__FEDERATION__;\n    plugins.forEach((plugin)=>{\n        if (__GLOBAL_PLUGIN__.findIndex((p)=>p.name === plugin.name) === -1) {\n            __GLOBAL_PLUGIN__.push(plugin);\n        } else {\n            warn(`The plugin ${plugin.name} has been registered.`);\n        }\n    });\n};\nconst getGlobalHostPlugins = ()=>nativeGlobal.__FEDERATION__.__GLOBAL_PLUGIN__;\nconst getPreloaded = (id)=>globalThis.__FEDERATION__.__PRELOADED_MAP__.get(id);\nconst setPreloaded = (id)=>globalThis.__FEDERATION__.__PRELOADED_MAP__.set(id, true);\n\nconst DEFAULT_SCOPE = 'default';\nconst DEFAULT_REMOTE_TYPE = 'global';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// those constants are based on https://www.rubydoc.info/gems/semantic_range/3.0.0/SemanticRange#BUILDIDENTIFIER-constant\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nconst buildIdentifier = '[0-9A-Za-z-]+';\nconst build = `(?:\\\\+(${buildIdentifier}(?:\\\\.${buildIdentifier})*))`;\nconst numericIdentifier = '0|[1-9]\\\\d*';\nconst numericIdentifierLoose = '[0-9]+';\nconst nonNumericIdentifier = '\\\\d*[a-zA-Z-][a-zA-Z0-9-]*';\nconst preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;\nconst preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\\\.${preReleaseIdentifierLoose})*))`;\nconst preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;\nconst preRelease = `(?:-(${preReleaseIdentifier}(?:\\\\.${preReleaseIdentifier})*))`;\nconst xRangeIdentifier = `${numericIdentifier}|x|X|\\\\*`;\nconst xRangePlain = `[v=\\\\s]*(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;\nconst hyphenRange = `^\\\\s*(${xRangePlain})\\\\s+-\\\\s+(${xRangePlain})\\\\s*$`;\nconst mainVersionLoose = `(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})`;\nconst loosePlain = `[v=\\\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;\nconst gtlt = '((?:<|>)?=?)';\nconst comparatorTrim = `(\\\\s*)${gtlt}\\\\s*(${loosePlain}|${xRangePlain})`;\nconst loneTilde = '(?:~>?)';\nconst tildeTrim = `(\\\\s*)${loneTilde}\\\\s+`;\nconst loneCaret = '(?:\\\\^)';\nconst caretTrim = `(\\\\s*)${loneCaret}\\\\s+`;\nconst star = '(<|>)?=?\\\\s*\\\\*';\nconst caret = `^${loneCaret}${xRangePlain}$`;\nconst mainVersion = `(${numericIdentifier})\\\\.(${numericIdentifier})\\\\.(${numericIdentifier})`;\nconst fullPlain = `v?${mainVersion}${preRelease}?${build}?`;\nconst tilde = `^${loneTilde}${xRangePlain}$`;\nconst xRange = `^${gtlt}\\\\s*${xRangePlain}$`;\nconst comparator = `^${gtlt}\\\\s*(${fullPlain})$|^$`;\n// copy from semver package\nconst gte0 = '^\\\\s*>=\\\\s*0.0.0\\\\s*$';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseRegex(source) {\n    return new RegExp(source);\n}\nfunction isXVersion(version) {\n    return !version || version.toLowerCase() === 'x' || version === '*';\n}\nfunction pipe(...fns) {\n    return (x)=>fns.reduce((v, f)=>f(v), x);\n}\nfunction extractComparator(comparatorString) {\n    return comparatorString.match(parseRegex(comparator));\n}\nfunction combineVersion(major, minor, patch, preRelease) {\n    const mainVersion = `${major}.${minor}.${patch}`;\n    if (preRelease) {\n        return `${mainVersion}-${preRelease}`;\n    }\n    return mainVersion;\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseHyphen(range) {\n    return range.replace(parseRegex(hyphenRange), (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease)=>{\n        if (isXVersion(fromMajor)) {\n            from = '';\n        } else if (isXVersion(fromMinor)) {\n            from = `>=${fromMajor}.0.0`;\n        } else if (isXVersion(fromPatch)) {\n            from = `>=${fromMajor}.${fromMinor}.0`;\n        } else {\n            from = `>=${from}`;\n        }\n        if (isXVersion(toMajor)) {\n            to = '';\n        } else if (isXVersion(toMinor)) {\n            to = `<${Number(toMajor) + 1}.0.0-0`;\n        } else if (isXVersion(toPatch)) {\n            to = `<${toMajor}.${Number(toMinor) + 1}.0-0`;\n        } else if (toPreRelease) {\n            to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;\n        } else {\n            to = `<=${to}`;\n        }\n        return `${from} ${to}`.trim();\n    });\n}\nfunction parseComparatorTrim(range) {\n    return range.replace(parseRegex(comparatorTrim), '$1$2$3');\n}\nfunction parseTildeTrim(range) {\n    return range.replace(parseRegex(tildeTrim), '$1~');\n}\nfunction parseCaretTrim(range) {\n    return range.replace(parseRegex(caretTrim), '$1^');\n}\nfunction parseCarets(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(caret), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                if (major === '0') {\n                    return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n                } else {\n                    return `>=${major}.${minor}.0 <${Number(major) + 1}.0.0-0`;\n                }\n            } else if (preRelease) {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                } else {\n                    return `>=${major}.${minor}.${patch}-${preRelease} <${Number(major) + 1}.0.0-0`;\n                }\n            } else {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                }\n                return `>=${major}.${minor}.${patch} <${Number(major) + 1}.0.0-0`;\n            }\n        })).join(' ');\n}\nfunction parseTildes(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(tilde), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n            } else if (preRelease) {\n                return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n        })).join(' ');\n}\nfunction parseXRanges(range) {\n    return range.split(/\\s+/).map((rangeVersion)=>rangeVersion.trim().replace(parseRegex(xRange), (ret, gtlt, major, minor, patch, preRelease)=>{\n            const isXMajor = isXVersion(major);\n            const isXMinor = isXMajor || isXVersion(minor);\n            const isXPatch = isXMinor || isXVersion(patch);\n            if (gtlt === '=' && isXPatch) {\n                gtlt = '';\n            }\n            preRelease = '';\n            if (isXMajor) {\n                if (gtlt === '>' || gtlt === '<') {\n                    // nothing is allowed\n                    return '<0.0.0-0';\n                } else {\n                    // nothing is forbidden\n                    return '*';\n                }\n            } else if (gtlt && isXPatch) {\n                // replace X with 0\n                if (isXMinor) {\n                    minor = 0;\n                }\n                patch = 0;\n                if (gtlt === '>') {\n                    // >1 => >=2.0.0\n                    // >1.2 => >=1.3.0\n                    gtlt = '>=';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                        minor = 0;\n                        patch = 0;\n                    } else {\n                        minor = Number(minor) + 1;\n                        patch = 0;\n                    }\n                } else if (gtlt === '<=') {\n                    // <=0.7.x is actually <0.8.0, since any 0.7.x should pass\n                    // Similarly, <=7.x is actually <8.0.0, etc.\n                    gtlt = '<';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                    } else {\n                        minor = Number(minor) + 1;\n                    }\n                }\n                if (gtlt === '<') {\n                    preRelease = '-0';\n                }\n                return `${gtlt + major}.${minor}.${patch}${preRelease}`;\n            } else if (isXMinor) {\n                return `>=${major}.0.0${preRelease} <${Number(major) + 1}.0.0-0`;\n            } else if (isXPatch) {\n                return `>=${major}.${minor}.0${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return ret;\n        })).join(' ');\n}\nfunction parseStar(range) {\n    return range.trim().replace(parseRegex(star), '');\n}\nfunction parseGTE0(comparatorString) {\n    return comparatorString.trim().replace(parseRegex(gte0), '');\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction compareAtom(rangeAtom, versionAtom) {\n    rangeAtom = Number(rangeAtom) || rangeAtom;\n    versionAtom = Number(versionAtom) || versionAtom;\n    if (rangeAtom > versionAtom) {\n        return 1;\n    }\n    if (rangeAtom === versionAtom) {\n        return 0;\n    }\n    return -1;\n}\nfunction comparePreRelease(rangeAtom, versionAtom) {\n    const { preRelease: rangePreRelease } = rangeAtom;\n    const { preRelease: versionPreRelease } = versionAtom;\n    if (rangePreRelease === undefined && Boolean(versionPreRelease)) {\n        return 1;\n    }\n    if (Boolean(rangePreRelease) && versionPreRelease === undefined) {\n        return -1;\n    }\n    if (rangePreRelease === undefined && versionPreRelease === undefined) {\n        return 0;\n    }\n    for(let i = 0, n = rangePreRelease.length; i <= n; i++){\n        const rangeElement = rangePreRelease[i];\n        const versionElement = versionPreRelease[i];\n        if (rangeElement === versionElement) {\n            continue;\n        }\n        if (rangeElement === undefined && versionElement === undefined) {\n            return 0;\n        }\n        if (!rangeElement) {\n            return 1;\n        }\n        if (!versionElement) {\n            return -1;\n        }\n        return compareAtom(rangeElement, versionElement);\n    }\n    return 0;\n}\nfunction compareVersion(rangeAtom, versionAtom) {\n    return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);\n}\nfunction eq(rangeAtom, versionAtom) {\n    return rangeAtom.version === versionAtom.version;\n}\nfunction compare(rangeAtom, versionAtom) {\n    switch(rangeAtom.operator){\n        case '':\n        case '=':\n            return eq(rangeAtom, versionAtom);\n        case '>':\n            return compareVersion(rangeAtom, versionAtom) < 0;\n        case '>=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;\n        case '<':\n            return compareVersion(rangeAtom, versionAtom) > 0;\n        case '<=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;\n        case undefined:\n            {\n                // mean * or x -> all versions\n                return true;\n            }\n        default:\n            return false;\n    }\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseComparatorString(range) {\n    return pipe(// handle caret\n    // ^ --> * (any, kinda silly)\n    // ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n    // ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n    // ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n    // ^1.2.3 --> >=1.2.3 <2.0.0-0\n    // ^1.2.0 --> >=1.2.0 <2.0.0-0\n    parseCarets, // handle tilde\n    // ~, ~> --> * (any, kinda silly)\n    // ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n    // ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n    // ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n    // ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n    // ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n    parseTildes, parseXRanges, parseStar)(range);\n}\nfunction parseRange(range) {\n    return pipe(// handle hyphenRange\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    parseHyphen, // handle trim comparator\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    parseComparatorTrim, // handle trim tilde\n    // `~ 1.2.3` => `~1.2.3`\n    parseTildeTrim, // handle trim caret\n    // `^ 1.2.3` => `^1.2.3`\n    parseCaretTrim)(range.trim()).split(/\\s+/).join(' ');\n}\nfunction satisfy(version, range) {\n    if (!version) {\n        return false;\n    }\n    const parsedRange = parseRange(range);\n    const parsedComparator = parsedRange.split(' ').map((rangeVersion)=>parseComparatorString(rangeVersion)).join(' ');\n    const comparators = parsedComparator.split(/\\s+/).map((comparator)=>parseGTE0(comparator));\n    const extractedVersion = extractComparator(version);\n    if (!extractedVersion) {\n        return false;\n    }\n    const [, versionOperator, , versionMajor, versionMinor, versionPatch, versionPreRelease] = extractedVersion;\n    const versionAtom = {\n        operator: versionOperator,\n        version: combineVersion(versionMajor, versionMinor, versionPatch, versionPreRelease),\n        major: versionMajor,\n        minor: versionMinor,\n        patch: versionPatch,\n        preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split('.')\n    };\n    for (const comparator of comparators){\n        const extractedComparator = extractComparator(comparator);\n        if (!extractedComparator) {\n            return false;\n        }\n        const [, rangeOperator, , rangeMajor, rangeMinor, rangePatch, rangePreRelease] = extractedComparator;\n        const rangeAtom = {\n            operator: rangeOperator,\n            version: combineVersion(rangeMajor, rangeMinor, rangePatch, rangePreRelease),\n            major: rangeMajor,\n            minor: rangeMinor,\n            patch: rangePatch,\n            preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split('.')\n        };\n        if (!compare(rangeAtom, versionAtom)) {\n            return false; // early return\n        }\n    }\n    return true;\n}\n\nfunction formatShare(shareArgs, from, name, shareStrategy) {\n    let get;\n    if ('get' in shareArgs) {\n        // eslint-disable-next-line prefer-destructuring\n        get = shareArgs.get;\n    } else if ('lib' in shareArgs) {\n        get = ()=>Promise.resolve(shareArgs.lib);\n    } else {\n        get = ()=>Promise.resolve(()=>{\n                throw new Error(`Can not get shared '${name}'!`);\n            });\n    }\n    if (shareArgs.strategy) {\n        warn(`\"shared.strategy is deprecated, please set in initOptions.shareStrategy instead!\"`);\n    }\n    var _shareArgs_version, _shareArgs_scope, _shareArgs_strategy;\n    return polyfills._extends({\n        deps: [],\n        useIn: [],\n        from,\n        loading: null\n    }, shareArgs, {\n        shareConfig: polyfills._extends({\n            requiredVersion: `^${shareArgs.version}`,\n            singleton: false,\n            eager: false,\n            strictVersion: false\n        }, shareArgs.shareConfig),\n        get,\n        loaded: (shareArgs == null ? void 0 : shareArgs.loaded) || 'lib' in shareArgs ? true : undefined,\n        version: (_shareArgs_version = shareArgs.version) != null ? _shareArgs_version : '0',\n        scope: Array.isArray(shareArgs.scope) ? shareArgs.scope : [\n            (_shareArgs_scope = shareArgs.scope) != null ? _shareArgs_scope : 'default'\n        ],\n        strategy: ((_shareArgs_strategy = shareArgs.strategy) != null ? _shareArgs_strategy : shareStrategy) || 'version-first'\n    });\n}\nfunction formatShareConfigs(globalOptions, userOptions) {\n    const shareArgs = userOptions.shared || {};\n    const from = userOptions.name;\n    const shareInfos = Object.keys(shareArgs).reduce((res, pkgName)=>{\n        const arrayShareArgs = arrayOptions(shareArgs[pkgName]);\n        res[pkgName] = res[pkgName] || [];\n        arrayShareArgs.forEach((shareConfig)=>{\n            res[pkgName].push(formatShare(shareConfig, from, pkgName, userOptions.shareStrategy));\n        });\n        return res;\n    }, {});\n    const shared = polyfills._extends({}, globalOptions.shared);\n    Object.keys(shareInfos).forEach((shareKey)=>{\n        if (!shared[shareKey]) {\n            shared[shareKey] = shareInfos[shareKey];\n        } else {\n            shareInfos[shareKey].forEach((newUserSharedOptions)=>{\n                const isSameVersion = shared[shareKey].find((sharedVal)=>sharedVal.version === newUserSharedOptions.version);\n                if (!isSameVersion) {\n                    shared[shareKey].push(newUserSharedOptions);\n                }\n            });\n        }\n    });\n    return {\n        shared,\n        shareInfos\n    };\n}\nfunction versionLt(a, b) {\n    const transformInvalidVersion = (version)=>{\n        const isNumberVersion = !Number.isNaN(Number(version));\n        if (isNumberVersion) {\n            const splitArr = version.split('.');\n            let validVersion = version;\n            for(let i = 0; i < 3 - splitArr.length; i++){\n                validVersion += '.0';\n            }\n            return validVersion;\n        }\n        return version;\n    };\n    if (satisfy(transformInvalidVersion(a), `<=${transformInvalidVersion(b)}`)) {\n        return true;\n    } else {\n        return false;\n    }\n}\nconst findVersion = (shareVersionMap, cb)=>{\n    const callback = cb || function(prev, cur) {\n        return versionLt(prev, cur);\n    };\n    return Object.keys(shareVersionMap).reduce((prev, cur)=>{\n        if (!prev) {\n            return cur;\n        }\n        if (callback(prev, cur)) {\n            return cur;\n        }\n        // default version is '0' https://github.com/webpack/webpack/blob/main/lib/sharing/ProvideSharedModule.js#L136\n        if (prev === '0') {\n            return cur;\n        }\n        return prev;\n    }, 0);\n};\nconst isLoaded = (shared)=>{\n    return Boolean(shared.loaded) || typeof shared.lib === 'function';\n};\nfunction findSingletonVersionOrderByVersion(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        return !isLoaded(versions[prev]) && versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction findSingletonVersionOrderByLoaded(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        if (isLoaded(versions[cur])) {\n            if (isLoaded(versions[prev])) {\n                return Boolean(versionLt(prev, cur));\n            } else {\n                return true;\n            }\n        }\n        if (isLoaded(versions[prev])) {\n            return false;\n        }\n        return versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction getFindShareFunction(strategy) {\n    if (strategy === 'loaded-first') {\n        return findSingletonVersionOrderByLoaded;\n    }\n    return findSingletonVersionOrderByVersion;\n}\nfunction getRegisteredShare(localShareScopeMap, pkgName, shareInfo, resolveShare) {\n    if (!localShareScopeMap) {\n        return;\n    }\n    const { shareConfig, scope = DEFAULT_SCOPE, strategy } = shareInfo;\n    const scopes = Array.isArray(scope) ? scope : [\n        scope\n    ];\n    for (const sc of scopes){\n        if (shareConfig && localShareScopeMap[sc] && localShareScopeMap[sc][pkgName]) {\n            const { requiredVersion } = shareConfig;\n            const findShareFunction = getFindShareFunction(strategy);\n            const maxOrSingletonVersion = findShareFunction(localShareScopeMap, sc, pkgName);\n            //@ts-ignore\n            const defaultResolver = ()=>{\n                if (shareConfig.singleton) {\n                    if (typeof requiredVersion === 'string' && !satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        const msg = `Version ${maxOrSingletonVersion} from ${maxOrSingletonVersion && localShareScopeMap[sc][pkgName][maxOrSingletonVersion].from} of shared singleton module ${pkgName} does not satisfy the requirement of ${shareInfo.from} which needs ${requiredVersion})`;\n                        if (shareConfig.strictVersion) {\n                            error(msg);\n                        } else {\n                            warn(msg);\n                        }\n                    }\n                    return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                } else {\n                    if (requiredVersion === false || requiredVersion === '*') {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    if (satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    for (const [versionKey, versionValue] of Object.entries(localShareScopeMap[sc][pkgName])){\n                        if (satisfy(versionKey, requiredVersion)) {\n                            return versionValue;\n                        }\n                    }\n                }\n            };\n            const params = {\n                shareScopeMap: localShareScopeMap,\n                scope: sc,\n                pkgName,\n                version: maxOrSingletonVersion,\n                GlobalFederation: Global.__FEDERATION__,\n                resolver: defaultResolver\n            };\n            const resolveShared = resolveShare.emit(params) || params;\n            return resolveShared.resolver();\n        }\n    }\n}\nfunction getGlobalShareScope() {\n    return Global.__FEDERATION__.__SHARE__;\n}\nfunction getTargetSharedOptions(options) {\n    const { pkgName, extraOptions, shareInfos } = options;\n    const defaultResolver = (sharedOptions)=>{\n        if (!sharedOptions) {\n            return undefined;\n        }\n        const shareVersionMap = {};\n        sharedOptions.forEach((shared)=>{\n            shareVersionMap[shared.version] = shared;\n        });\n        const callback = function(prev, cur) {\n            return !isLoaded(shareVersionMap[prev]) && versionLt(prev, cur);\n        };\n        const maxVersion = findVersion(shareVersionMap, callback);\n        return shareVersionMap[maxVersion];\n    };\n    var _extraOptions_resolver;\n    const resolver = (_extraOptions_resolver = extraOptions == null ? void 0 : extraOptions.resolver) != null ? _extraOptions_resolver : defaultResolver;\n    return Object.assign({}, resolver(shareInfos[pkgName]), extraOptions == null ? void 0 : extraOptions.customShareInfo);\n}\n\nexports.DEFAULT_REMOTE_TYPE = DEFAULT_REMOTE_TYPE;\nexports.DEFAULT_SCOPE = DEFAULT_SCOPE;\nexports.Global = Global;\nexports.addGlobalSnapshot = addGlobalSnapshot;\nexports.addUniqueItem = addUniqueItem;\nexports.arrayOptions = arrayOptions;\nexports.assert = assert;\nexports.error = error;\nexports.formatShareConfigs = formatShareConfigs;\nexports.getBuilderId = getBuilderId;\nexports.getFMId = getFMId;\nexports.getGlobalFederationConstructor = getGlobalFederationConstructor;\nexports.getGlobalFederationInstance = getGlobalFederationInstance;\nexports.getGlobalHostPlugins = getGlobalHostPlugins;\nexports.getGlobalShareScope = getGlobalShareScope;\nexports.getGlobalSnapshot = getGlobalSnapshot;\nexports.getGlobalSnapshotInfoByModuleInfo = getGlobalSnapshotInfoByModuleInfo;\nexports.getInfoWithoutType = getInfoWithoutType;\nexports.getPreloaded = getPreloaded;\nexports.getRegisteredShare = getRegisteredShare;\nexports.getRemoteEntryExports = getRemoteEntryExports;\nexports.getRemoteEntryInfoFromSnapshot = getRemoteEntryInfoFromSnapshot;\nexports.getTargetSharedOptions = getTargetSharedOptions;\nexports.getTargetSnapshotInfoByModuleInfo = getTargetSnapshotInfoByModuleInfo;\nexports.globalLoading = globalLoading;\nexports.isObject = isObject;\nexports.isPlainObject = isPlainObject;\nexports.isPureRemoteEntry = isPureRemoteEntry;\nexports.isRemoteInfoWithEntry = isRemoteInfoWithEntry;\nexports.logger = logger;\nexports.nativeGlobal = nativeGlobal;\nexports.registerGlobalPlugins = registerGlobalPlugins;\nexports.resetFederationGlobalInfo = resetFederationGlobalInfo;\nexports.setGlobalFederationConstructor = setGlobalFederationConstructor;\nexports.setGlobalFederationInstance = setGlobalFederationInstance;\nexports.setGlobalSnapshotInfoByModuleInfo = setGlobalSnapshotInfoByModuleInfo;\nexports.setPreloaded = setPreloaded;\nexports.warn = warn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2214\n\n}");

/***/ }),

/***/ 3091:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("{\n\nvar runtime = __webpack_require__(3197);\nvar constant = __webpack_require__(4179);\nvar sdk = __webpack_require__(9468);\nvar polyfills = __webpack_require__(9399);\n\nfunction _interopNamespaceDefault(e) {\n  var n = Object.create(null);\n  if (e) {\n    Object.keys(e).forEach(function (k) {\n      if (k !== 'default') {\n        var d = Object.getOwnPropertyDescriptor(e, k);\n        Object.defineProperty(n, k, d.get ? d : {\n          enumerable: true,\n          get: function () { return e[k]; }\n        });\n      }\n    });\n  }\n  n.default = e;\n  return Object.freeze(n);\n}\n\nvar runtime__namespace = /*#__PURE__*/_interopNamespaceDefault(runtime);\n\nfunction attachShareScopeMap(webpackRequire) {\n    if (!webpackRequire.S || webpackRequire.federation.hasAttachShareScopeMap || !webpackRequire.federation.instance || !webpackRequire.federation.instance.shareScopeMap) {\n        return;\n    }\n    webpackRequire.S = webpackRequire.federation.instance.shareScopeMap;\n    webpackRequire.federation.hasAttachShareScopeMap = true;\n}\n\nfunction remotes(options) {\n    const { chunkId, promises, chunkMapping, idToExternalAndNameMapping, webpackRequire, idToRemoteMap } = options;\n    attachShareScopeMap(webpackRequire);\n    if (webpackRequire.o(chunkMapping, chunkId)) {\n        chunkMapping[chunkId].forEach((id)=>{\n            let getScope = webpackRequire.R;\n            if (!getScope) {\n                getScope = [];\n            }\n            const data = idToExternalAndNameMapping[id];\n            const remoteInfos = idToRemoteMap[id];\n            // @ts-ignore seems not work\n            if (getScope.indexOf(data) >= 0) {\n                return;\n            }\n            // @ts-ignore seems not work\n            getScope.push(data);\n            if (data.p) {\n                return promises.push(data.p);\n            }\n            const onError = (error)=>{\n                if (!error) {\n                    error = new Error('Container missing');\n                }\n                if (typeof error.message === 'string') {\n                    error.message += `\\nwhile loading \"${data[1]}\" from ${data[2]}`;\n                }\n                webpackRequire.m[id] = ()=>{\n                    throw error;\n                };\n                data.p = 0;\n            };\n            const handleFunction = (fn, arg1, arg2, d, next, first)=>{\n                try {\n                    const promise = fn(arg1, arg2);\n                    if (promise && promise.then) {\n                        const p = promise.then((result)=>next(result, d), onError);\n                        if (first) {\n                            promises.push(data.p = p);\n                        } else {\n                            return p;\n                        }\n                    } else {\n                        return next(promise, d, first);\n                    }\n                } catch (error) {\n                    onError(error);\n                }\n            };\n            const onExternal = (external, _, first)=>external ? handleFunction(webpackRequire.I, data[0], 0, external, onInitialized, first) : onError();\n            // eslint-disable-next-line no-var\n            var onInitialized = (_, external, first)=>handleFunction(external.get, data[1], getScope, 0, onFactory, first);\n            // eslint-disable-next-line no-var\n            var onFactory = (factory)=>{\n                data.p = 1;\n                webpackRequire.m[id] = (module)=>{\n                    module.exports = factory();\n                };\n            };\n            const onRemoteLoaded = ()=>{\n                try {\n                    const remoteName = sdk.decodeName(remoteInfos[0].name, sdk.ENCODE_NAME_PREFIX);\n                    const remoteModuleName = remoteName + data[1].slice(1);\n                    return webpackRequire.federation.instance.loadRemote(remoteModuleName, {\n                        loadFactory: false,\n                        from: 'build'\n                    });\n                } catch (error) {\n                    onError(error);\n                }\n            };\n            const useRuntimeLoad = remoteInfos.length === 1 && constant.FEDERATION_SUPPORTED_TYPES.includes(remoteInfos[0].externalType) && remoteInfos[0].name;\n            if (useRuntimeLoad) {\n                handleFunction(onRemoteLoaded, data[2], 0, 0, onFactory, 1);\n            } else {\n                handleFunction(webpackRequire, data[2], 0, 0, onExternal, 1);\n            }\n        });\n    }\n}\n\nfunction consumes(options) {\n    const { chunkId, promises, chunkMapping, installedModules, moduleToHandlerMapping, webpackRequire } = options;\n    attachShareScopeMap(webpackRequire);\n    if (webpackRequire.o(chunkMapping, chunkId)) {\n        chunkMapping[chunkId].forEach((id)=>{\n            if (webpackRequire.o(installedModules, id)) {\n                return promises.push(installedModules[id]);\n            }\n            const onFactory = (factory)=>{\n                installedModules[id] = 0;\n                webpackRequire.m[id] = (module)=>{\n                    delete webpackRequire.c[id];\n                    module.exports = factory();\n                };\n            };\n            const onError = (error)=>{\n                delete installedModules[id];\n                webpackRequire.m[id] = (module)=>{\n                    delete webpackRequire.c[id];\n                    throw error;\n                };\n            };\n            try {\n                const federationInstance = webpackRequire.federation.instance;\n                if (!federationInstance) {\n                    throw new Error('Federation instance not found!');\n                }\n                const { shareKey, getter, shareInfo } = moduleToHandlerMapping[id];\n                const promise = federationInstance.loadShare(shareKey, {\n                    customShareInfo: shareInfo\n                }).then((factory)=>{\n                    if (factory === false) {\n                        return getter();\n                    }\n                    return factory;\n                });\n                if (promise.then) {\n                    promises.push(installedModules[id] = promise.then(onFactory).catch(onError));\n                } else {\n                    // @ts-ignore maintain previous logic\n                    onFactory(promise);\n                }\n            } catch (e) {\n                onError(e);\n            }\n        });\n    }\n}\n\nfunction initializeSharing({ shareScopeName, webpackRequire, initPromises, initTokens, initScope }) {\n    if (!initScope) initScope = [];\n    const mfInstance = webpackRequire.federation.instance;\n    // handling circular init calls\n    var initToken = initTokens[shareScopeName];\n    if (!initToken) initToken = initTokens[shareScopeName] = {\n        from: mfInstance.name\n    };\n    if (initScope.indexOf(initToken) >= 0) return;\n    initScope.push(initToken);\n    const promise = initPromises[shareScopeName];\n    if (promise) return promise;\n    var warn = (msg)=>typeof console !== 'undefined' && console.warn && console.warn(msg);\n    var initExternal = (id)=>{\n        var handleError = (err)=>warn('Initialization of sharing external failed: ' + err);\n        try {\n            var module = webpackRequire(id);\n            if (!module) return;\n            var initFn = (module)=>module && module.init && // @ts-ignore compat legacy mf shared behavior\n                module.init(webpackRequire.S[shareScopeName], initScope);\n            if (module.then) return promises.push(module.then(initFn, handleError));\n            var initResult = initFn(module);\n            // @ts-ignore\n            if (initResult && typeof initResult !== 'boolean' && initResult.then) // @ts-ignore\n            return promises.push(initResult['catch'](handleError));\n        } catch (err) {\n            handleError(err);\n        }\n    };\n    const promises = mfInstance.initializeSharing(shareScopeName, {\n        strategy: mfInstance.options.shareStrategy,\n        initScope,\n        from: 'build'\n    });\n    attachShareScopeMap(webpackRequire);\n    const bundlerRuntimeRemotesOptions = webpackRequire.federation.bundlerRuntimeOptions.remotes;\n    if (bundlerRuntimeRemotesOptions) {\n        Object.keys(bundlerRuntimeRemotesOptions.idToRemoteMap).forEach((moduleId)=>{\n            const info = bundlerRuntimeRemotesOptions.idToRemoteMap[moduleId];\n            const externalModuleId = bundlerRuntimeRemotesOptions.idToExternalAndNameMapping[moduleId][2];\n            if (info.length > 1) {\n                initExternal(externalModuleId);\n            } else if (info.length === 1) {\n                const remoteInfo = info[0];\n                if (!constant.FEDERATION_SUPPORTED_TYPES.includes(remoteInfo.externalType)) {\n                    initExternal(externalModuleId);\n                }\n            }\n        });\n    }\n    if (!promises.length) {\n        return initPromises[shareScopeName] = true;\n    }\n    return initPromises[shareScopeName] = Promise.all(promises).then(()=>initPromises[shareScopeName] = true);\n}\n\nfunction handleInitialConsumes(options) {\n    const { moduleId, moduleToHandlerMapping, webpackRequire } = options;\n    const federationInstance = webpackRequire.federation.instance;\n    if (!federationInstance) {\n        throw new Error('Federation instance not found!');\n    }\n    const { shareKey, shareInfo } = moduleToHandlerMapping[moduleId];\n    try {\n        return federationInstance.loadShareSync(shareKey, {\n            customShareInfo: shareInfo\n        });\n    } catch (err) {\n        console.error('loadShareSync failed! The function should not be called unless you set \"eager:true\". If you do not set it, and encounter this issue, you can check whether an async boundary is implemented.');\n        console.error('The original error message is as follows: ');\n        throw err;\n    }\n}\nfunction installInitialConsumes(options) {\n    const { moduleToHandlerMapping, webpackRequire, installedModules, initialConsumes } = options;\n    initialConsumes.forEach((id)=>{\n        webpackRequire.m[id] = (module)=>{\n            // Handle scenario when module is used synchronously\n            installedModules[id] = 0;\n            delete webpackRequire.c[id];\n            const factory = handleInitialConsumes({\n                moduleId: id,\n                moduleToHandlerMapping,\n                webpackRequire\n            });\n            if (typeof factory !== 'function') {\n                throw new Error(`Shared module is not available for eager consumption: ${id}`);\n            }\n            module.exports = factory();\n        };\n    });\n}\n\nfunction initContainerEntry(options) {\n    const { webpackRequire, shareScope, initScope, shareScopeKey, remoteEntryInitOptions } = options;\n    if (!webpackRequire.S) return;\n    if (!webpackRequire.federation || !webpackRequire.federation.instance || !webpackRequire.federation.initOptions) return;\n    const federationInstance = webpackRequire.federation.instance;\n    var name = shareScopeKey || 'default';\n    federationInstance.initOptions(polyfills._extends({\n        name: webpackRequire.federation.initOptions.name,\n        remotes: []\n    }, remoteEntryInitOptions));\n    federationInstance.initShareScopeMap(name, shareScope, {\n        hostShareScopeMap: (remoteEntryInitOptions == null ? void 0 : remoteEntryInitOptions.shareScopeMap) || {}\n    });\n    if (webpackRequire.federation.attachShareScopeMap) {\n        webpackRequire.federation.attachShareScopeMap(webpackRequire);\n    }\n    if (typeof webpackRequire.federation.prefetch === 'function') {\n        webpackRequire.federation.prefetch();\n    }\n    // @ts-ignore\n    return webpackRequire.I(name, initScope);\n}\n\nconst federation = {\n    runtime: runtime__namespace,\n    instance: undefined,\n    initOptions: undefined,\n    bundlerRuntime: {\n        remotes,\n        consumes,\n        I: initializeSharing,\n        S: {},\n        installInitialConsumes,\n        initContainerEntry\n    },\n    attachShareScopeMap,\n    bundlerRuntimeOptions: {}\n};\n\nmodule.exports = federation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3091\n\n}");

/***/ }),

/***/ 3197:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar polyfills = __webpack_require__(6689);\nvar sdk = __webpack_require__(9468);\nvar share = __webpack_require__(2214);\nvar errorCodes = __webpack_require__(3767);\n\n// Function to match a remote with its name and expose\n// id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n// id: alias(app1) + expose(button) = app1/button\n// id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\nfunction matchRemoteWithNameAndExpose(remotes, id) {\n    for (const remote of remotes){\n        // match pkgName\n        const isNameMatched = id.startsWith(remote.name);\n        let expose = id.replace(remote.name, '');\n        if (isNameMatched) {\n            if (expose.startsWith('/')) {\n                const pkgNameOrAlias = remote.name;\n                expose = `.${expose}`;\n                return {\n                    pkgNameOrAlias,\n                    expose,\n                    remote\n                };\n            } else if (expose === '') {\n                return {\n                    pkgNameOrAlias: remote.name,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n        // match alias\n        const isAliasMatched = remote.alias && id.startsWith(remote.alias);\n        let exposeWithAlias = remote.alias && id.replace(remote.alias, '');\n        if (remote.alias && isAliasMatched) {\n            if (exposeWithAlias && exposeWithAlias.startsWith('/')) {\n                const pkgNameOrAlias = remote.alias;\n                exposeWithAlias = `.${exposeWithAlias}`;\n                return {\n                    pkgNameOrAlias,\n                    expose: exposeWithAlias,\n                    remote\n                };\n            } else if (exposeWithAlias === '') {\n                return {\n                    pkgNameOrAlias: remote.alias,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n    }\n    return;\n}\n// Function to match a remote with its name or alias\nfunction matchRemote(remotes, nameOrAlias) {\n    for (const remote of remotes){\n        const isNameMatched = nameOrAlias === remote.name;\n        if (isNameMatched) {\n            return remote;\n        }\n        const isAliasMatched = remote.alias && nameOrAlias === remote.alias;\n        if (isAliasMatched) {\n            return remote;\n        }\n    }\n    return;\n}\n\nfunction registerPlugins$1(plugins, hookInstances) {\n    const globalPlugins = share.getGlobalHostPlugins();\n    // Incorporate global plugins\n    if (globalPlugins.length > 0) {\n        globalPlugins.forEach((plugin)=>{\n            if (plugins == null ? void 0 : plugins.find((item)=>item.name !== plugin.name)) {\n                plugins.push(plugin);\n            }\n        });\n    }\n    if (plugins && plugins.length > 0) {\n        plugins.forEach((plugin)=>{\n            hookInstances.forEach((hookInstance)=>{\n                hookInstance.applyPlugin(plugin);\n            });\n        });\n    }\n    return plugins;\n}\n\nasync function loadEsmEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                import(/* webpackIgnore: true */ entry).then(resolve).catch(reject);\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nasync function loadSystemJsEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                //@ts-ignore\n                if (false) // removed by dead control flow\n{} else {\n                    new Function('callbacks', `System.import(\"${entry}\").then(callbacks[0]).catch(callbacks[1])`)([\n                        resolve,\n                        reject\n                    ]);\n                }\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nasync function loadEntryScript({ name, globalName, entry, createScriptHook }) {\n    const { entryExports: remoteEntryExports } = share.getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScript(entry, {\n        attrs: {},\n        createScriptHook: (url, attrs)=>{\n            const res = createScriptHook.emit({\n                url,\n                attrs\n            });\n            if (!res) return;\n            if (res instanceof HTMLScriptElement) {\n                return res;\n            }\n            if ('script' in res || 'timeout' in res) {\n                return res;\n            }\n            return;\n        }\n    }).then(()=>{\n        const { remoteEntryKey, entryExports } = share.getRemoteEntryExports(name, globalName);\n        share.assert(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {\n            remoteName: name,\n            remoteEntryUrl: entry,\n            remoteEntryKey\n        }));\n        return entryExports;\n    }).catch((e)=>{\n        throw e;\n    });\n}\nasync function loadEntryDom({ remoteInfo, remoteEntryExports, createScriptHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    switch(type){\n        case 'esm':\n        case 'module':\n            return loadEsmEntry({\n                entry,\n                remoteEntryExports\n            });\n        case 'system':\n            return loadSystemJsEntry({\n                entry,\n                remoteEntryExports\n            });\n        default:\n            return loadEntryScript({\n                entry,\n                globalName,\n                name,\n                createScriptHook\n            });\n    }\n}\nasync function loadEntryNode({ remoteInfo, createScriptHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    const { entryExports: remoteEntryExports } = share.getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScriptNode(entry, {\n        attrs: {\n            name,\n            globalName,\n            type\n        },\n        createScriptHook: (url, attrs)=>{\n            const res = createScriptHook.emit({\n                url,\n                attrs\n            });\n            if (!res) return;\n            if ('url' in res) {\n                return res;\n            }\n            return;\n        }\n    }).then(()=>{\n        const { remoteEntryKey, entryExports } = share.getRemoteEntryExports(name, globalName);\n        share.assert(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {\n            remoteName: name,\n            remoteEntryUrl: entry,\n            remoteEntryKey\n        }));\n        return entryExports;\n    }).catch((e)=>{\n        throw e;\n    });\n}\nfunction getRemoteEntryUniqueKey(remoteInfo) {\n    const { entry, name } = remoteInfo;\n    return sdk.composeKeyWithSeparator(name, entry);\n}\nasync function getRemoteEntry({ origin, remoteEntryExports, remoteInfo }) {\n    const uniqueKey = getRemoteEntryUniqueKey(remoteInfo);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    if (!share.globalLoading[uniqueKey]) {\n        const loadEntryHook = origin.remoteHandler.hooks.lifecycle.loadEntry;\n        const createScriptHook = origin.loaderHook.lifecycle.createScript;\n        share.globalLoading[uniqueKey] = loadEntryHook.emit({\n            createScriptHook,\n            remoteInfo,\n            remoteEntryExports\n        }).then((res)=>{\n            if (res) {\n                return res;\n            }\n            return sdk.isBrowserEnv() ? loadEntryDom({\n                remoteInfo,\n                remoteEntryExports,\n                createScriptHook\n            }) : loadEntryNode({\n                remoteInfo,\n                createScriptHook\n            });\n        });\n    }\n    return share.globalLoading[uniqueKey];\n}\nfunction getRemoteInfo(remote) {\n    return polyfills._extends({}, remote, {\n        entry: 'entry' in remote ? remote.entry : '',\n        type: remote.type || share.DEFAULT_REMOTE_TYPE,\n        entryGlobalName: remote.entryGlobalName || remote.name,\n        shareScope: remote.shareScope || share.DEFAULT_SCOPE\n    });\n}\n\nlet Module = class Module {\n    async getEntry() {\n        if (this.remoteEntryExports) {\n            return this.remoteEntryExports;\n        }\n        // Get remoteEntry.js\n        const remoteEntryExports = await getRemoteEntry({\n            origin: this.host,\n            remoteInfo: this.remoteInfo,\n            remoteEntryExports: this.remoteEntryExports\n        });\n        share.assert(remoteEntryExports, `remoteEntryExports is undefined \\n ${sdk.safeToString(this.remoteInfo)}`);\n        this.remoteEntryExports = remoteEntryExports;\n        return this.remoteEntryExports;\n    }\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    async get(id, expose, options, remoteSnapshot) {\n        const { loadFactory = true } = options || {\n            loadFactory: true\n        };\n        // Get remoteEntry.js\n        const remoteEntryExports = await this.getEntry();\n        if (!this.inited) {\n            const localShareScopeMap = this.host.shareScopeMap;\n            const remoteShareScope = this.remoteInfo.shareScope || 'default';\n            if (!localShareScopeMap[remoteShareScope]) {\n                localShareScopeMap[remoteShareScope] = {};\n            }\n            const shareScope = localShareScopeMap[remoteShareScope];\n            const initScope = [];\n            const remoteEntryInitOptions = {\n                version: this.remoteInfo.version || ''\n            };\n            // Help to find host instance\n            Object.defineProperty(remoteEntryInitOptions, 'shareScopeMap', {\n                value: localShareScopeMap,\n                // remoteEntryInitOptions will be traversed and assigned during container init, ,so this attribute is not allowed to be traversed\n                enumerable: false\n            });\n            const initContainerOptions = await this.host.hooks.lifecycle.beforeInitContainer.emit({\n                shareScope,\n                // @ts-ignore shareScopeMap will be set by Object.defineProperty\n                remoteEntryInitOptions,\n                initScope,\n                remoteInfo: this.remoteInfo,\n                origin: this.host\n            });\n            if (typeof (remoteEntryExports == null ? void 0 : remoteEntryExports.init) === 'undefined') {\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_002, errorCodes.runtimeDescMap, {\n                    remoteName: name,\n                    remoteEntryUrl: this.remoteInfo.entry,\n                    remoteEntryKey: this.remoteInfo.entryGlobalName\n                }));\n            }\n            await remoteEntryExports.init(initContainerOptions.shareScope, initContainerOptions.initScope, initContainerOptions.remoteEntryInitOptions);\n            await this.host.hooks.lifecycle.initContainer.emit(polyfills._extends({}, initContainerOptions, {\n                id,\n                remoteSnapshot,\n                remoteEntryExports\n            }));\n        }\n        this.lib = remoteEntryExports;\n        this.inited = true;\n        let moduleFactory;\n        moduleFactory = await this.host.loaderHook.lifecycle.getModuleFactory.emit({\n            remoteEntryExports,\n            expose,\n            moduleInfo: this.remoteInfo\n        });\n        // get exposeGetter\n        if (!moduleFactory) {\n            moduleFactory = await remoteEntryExports.get(expose);\n        }\n        share.assert(moduleFactory, `${share.getFMId(this.remoteInfo)} remote don't export ${expose}.`);\n        const wrapModuleFactory = this.wraperFactory(moduleFactory, id);\n        if (!loadFactory) {\n            return wrapModuleFactory;\n        }\n        const exposeContent = await wrapModuleFactory();\n        return exposeContent;\n    }\n    wraperFactory(moduleFactory, id) {\n        function defineModuleId(res, id) {\n            if (res && typeof res === 'object' && Object.isExtensible(res) && !Object.getOwnPropertyDescriptor(res, Symbol.for('mf_module_id'))) {\n                Object.defineProperty(res, Symbol.for('mf_module_id'), {\n                    value: id,\n                    enumerable: false\n                });\n            }\n        }\n        if (moduleFactory instanceof Promise) {\n            return async ()=>{\n                const res = await moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        } else {\n            return ()=>{\n                const res = moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        }\n    }\n    constructor({ remoteInfo, host }){\n        this.inited = false;\n        this.lib = undefined;\n        this.remoteInfo = remoteInfo;\n        this.host = host;\n    }\n};\n\nclass SyncHook {\n    on(fn) {\n        if (typeof fn === 'function') {\n            this.listeners.add(fn);\n        }\n    }\n    once(fn) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const self = this;\n        this.on(function wrapper(...args) {\n            self.remove(wrapper);\n            // eslint-disable-next-line prefer-spread\n            return fn.apply(null, args);\n        });\n    }\n    emit(...data) {\n        let result;\n        if (this.listeners.size > 0) {\n            // eslint-disable-next-line prefer-spread\n            this.listeners.forEach((fn)=>{\n                result = fn(...data);\n            });\n        }\n        return result;\n    }\n    remove(fn) {\n        this.listeners.delete(fn);\n    }\n    removeAll() {\n        this.listeners.clear();\n    }\n    constructor(type){\n        this.type = '';\n        this.listeners = new Set();\n        if (type) {\n            this.type = type;\n        }\n    }\n}\n\nclass AsyncHook extends SyncHook {\n    emit(...data) {\n        let result;\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const call = (prev)=>{\n                if (prev === false) {\n                    return false; // Abort process\n                } else if (i < ls.length) {\n                    return Promise.resolve(ls[i++].apply(null, data)).then(call);\n                } else {\n                    return prev;\n                }\n            };\n            result = call();\n        }\n        return Promise.resolve(result);\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction checkReturnData(originalData, returnedData) {\n    if (!share.isObject(returnedData)) {\n        return false;\n    }\n    if (originalData !== returnedData) {\n        // eslint-disable-next-line no-restricted-syntax\n        for(const key in originalData){\n            if (!(key in returnedData)) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nclass SyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!share.isObject(data)) {\n            share.error(`The data for the \"${this.type}\" hook should be an object.`);\n        }\n        for (const fn of this.listeners){\n            try {\n                const tempData = fn(data);\n                if (checkReturnData(data, tempData)) {\n                    data = tempData;\n                } else {\n                    this.onerror(`A plugin returned an unacceptable value for the \"${this.type}\" type.`);\n                    break;\n                }\n            } catch (e) {\n                share.warn(e);\n                this.onerror(e);\n            }\n        }\n        return data;\n    }\n    constructor(type){\n        super(), this.onerror = share.error;\n        this.type = type;\n    }\n}\n\nclass AsyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!share.isObject(data)) {\n            share.error(`The response data for the \"${this.type}\" hook must be an object.`);\n        }\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const processError = (e)=>{\n                share.warn(e);\n                this.onerror(e);\n                return data;\n            };\n            const call = (prevData)=>{\n                if (checkReturnData(data, prevData)) {\n                    data = prevData;\n                    if (i < ls.length) {\n                        try {\n                            return Promise.resolve(ls[i++](data)).then(call, processError);\n                        } catch (e) {\n                            return processError(e);\n                        }\n                    }\n                } else {\n                    this.onerror(`A plugin returned an incorrect value for the \"${this.type}\" type.`);\n                }\n                return data;\n            };\n            return Promise.resolve(call(data));\n        }\n        return Promise.resolve(data);\n    }\n    constructor(type){\n        super(), this.onerror = share.error;\n        this.type = type;\n    }\n}\n\nclass PluginSystem {\n    applyPlugin(plugin) {\n        share.assert(share.isPlainObject(plugin), 'Plugin configuration is invalid.');\n        // The plugin's name is mandatory and must be unique\n        const pluginName = plugin.name;\n        share.assert(pluginName, 'A name must be provided by the plugin.');\n        if (!this.registerPlugins[pluginName]) {\n            this.registerPlugins[pluginName] = plugin;\n            Object.keys(this.lifecycle).forEach((key)=>{\n                const pluginLife = plugin[key];\n                if (pluginLife) {\n                    this.lifecycle[key].on(pluginLife);\n                }\n            });\n        }\n    }\n    removePlugin(pluginName) {\n        share.assert(pluginName, 'A name is required.');\n        const plugin = this.registerPlugins[pluginName];\n        share.assert(plugin, `The plugin \"${pluginName}\" is not registered.`);\n        Object.keys(plugin).forEach((key)=>{\n            if (key !== 'name') {\n                this.lifecycle[key].remove(plugin[key]);\n            }\n        });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    inherit({ lifecycle, registerPlugins }) {\n        Object.keys(lifecycle).forEach((hookName)=>{\n            share.assert(!this.lifecycle[hookName], `The hook \"${hookName}\" has a conflict and cannot be inherited.`);\n            this.lifecycle[hookName] = lifecycle[hookName];\n        });\n        Object.keys(registerPlugins).forEach((pluginName)=>{\n            share.assert(!this.registerPlugins[pluginName], `The plugin \"${pluginName}\" has a conflict and cannot be inherited.`);\n            this.applyPlugin(registerPlugins[pluginName]);\n        });\n    }\n    constructor(lifecycle){\n        this.registerPlugins = {};\n        this.lifecycle = lifecycle;\n        this.lifecycleKeys = Object.keys(lifecycle);\n    }\n}\n\nfunction defaultPreloadArgs(preloadConfig) {\n    return polyfills._extends({\n        resourceCategory: 'sync',\n        share: true,\n        depsRemote: true,\n        prefetchInterface: false\n    }, preloadConfig);\n}\nfunction formatPreloadArgs(remotes, preloadArgs) {\n    return preloadArgs.map((args)=>{\n        const remoteInfo = matchRemote(remotes, args.nameOrAlias);\n        share.assert(remoteInfo, `Unable to preload ${args.nameOrAlias} as it is not included in ${!remoteInfo && sdk.safeToString({\n            remoteInfo,\n            remotes\n        })}`);\n        return {\n            remote: remoteInfo,\n            preloadConfig: defaultPreloadArgs(args)\n        };\n    });\n}\nfunction normalizePreloadExposes(exposes) {\n    if (!exposes) {\n        return [];\n    }\n    return exposes.map((expose)=>{\n        if (expose === '.') {\n            return expose;\n        }\n        if (expose.startsWith('./')) {\n            return expose.replace('./', '');\n        }\n        return expose;\n    });\n}\nfunction preloadAssets(remoteInfo, host, assets, // It is used to distinguish preload from load remote parallel loading\nuseLinkPreload = true) {\n    const { cssAssets, jsAssetsWithoutEntry, entryAssets } = assets;\n    if (host.options.inBrowser) {\n        entryAssets.forEach((asset)=>{\n            const { moduleInfo } = asset;\n            const module = host.moduleCache.get(remoteInfo.name);\n            if (module) {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: module.remoteEntryExports\n                });\n            } else {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: undefined\n                });\n            }\n        });\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'style'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        } else {\n            const defaultAttrs = {\n                rel: 'stylesheet',\n                type: 'text/css'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteLink: false\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        }\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'script'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { link: linkEl, needAttach } = sdk.createLink({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(linkEl);\n            });\n        } else {\n            const defaultAttrs = {\n                fetchpriority: 'high',\n                type: (remoteInfo == null ? void 0 : remoteInfo.type) === 'module' ? 'module' : 'text/javascript'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { script: scriptEl, needAttach } = sdk.createScript({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createScriptHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createScript.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLScriptElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteScript: true\n                });\n                needAttach && document.head.appendChild(scriptEl);\n            });\n        }\n    }\n}\n\nfunction assignRemoteInfo(remoteInfo, remoteSnapshot) {\n    const remoteEntryInfo = share.getRemoteEntryInfoFromSnapshot(remoteSnapshot);\n    if (!remoteEntryInfo.url) {\n        share.error(`The attribute remoteEntry of ${remoteInfo.name} must not be undefined.`);\n    }\n    let entryUrl = sdk.getResourceUrl(remoteSnapshot, remoteEntryInfo.url);\n    if (!sdk.isBrowserEnv() && !entryUrl.startsWith('http')) {\n        entryUrl = `https:${entryUrl}`;\n    }\n    remoteInfo.type = remoteEntryInfo.type;\n    remoteInfo.entryGlobalName = remoteEntryInfo.globalName;\n    remoteInfo.entry = entryUrl;\n    remoteInfo.version = remoteSnapshot.version;\n    remoteInfo.buildVersion = remoteSnapshot.buildVersion;\n}\nfunction snapshotPlugin() {\n    return {\n        name: 'snapshot-plugin',\n        async afterResolve (args) {\n            const { remote, pkgNameOrAlias, expose, origin, remoteInfo } = args;\n            if (!share.isRemoteInfoWithEntry(remote) || !share.isPureRemoteEntry(remote)) {\n                const { remoteSnapshot, globalSnapshot } = await origin.snapshotHandler.loadRemoteSnapshotInfo(remote);\n                assignRemoteInfo(remoteInfo, remoteSnapshot);\n                // preloading assets\n                const preloadOptions = {\n                    remote,\n                    preloadConfig: {\n                        nameOrAlias: pkgNameOrAlias,\n                        exposes: [\n                            expose\n                        ],\n                        resourceCategory: 'sync',\n                        share: false,\n                        depsRemote: false\n                    }\n                };\n                const assets = await origin.remoteHandler.hooks.lifecycle.generatePreloadAssets.emit({\n                    origin,\n                    preloadOptions,\n                    remoteInfo,\n                    remote,\n                    remoteSnapshot,\n                    globalSnapshot\n                });\n                if (assets) {\n                    preloadAssets(remoteInfo, origin, assets, false);\n                }\n                return polyfills._extends({}, args, {\n                    remoteSnapshot\n                });\n            }\n            return args;\n        }\n    };\n}\n\n// name\n// name:version\nfunction splitId(id) {\n    const splitInfo = id.split(':');\n    if (splitInfo.length === 1) {\n        return {\n            name: splitInfo[0],\n            version: undefined\n        };\n    } else if (splitInfo.length === 2) {\n        return {\n            name: splitInfo[0],\n            version: splitInfo[1]\n        };\n    } else {\n        return {\n            name: splitInfo[1],\n            version: splitInfo[2]\n        };\n    }\n}\n// Traverse all nodes in moduleInfo and traverse the entire snapshot\nfunction traverseModuleInfo(globalSnapshot, remoteInfo, traverse, isRoot, memo = {}, remoteSnapshot) {\n    const id = share.getFMId(remoteInfo);\n    const { value: snapshotValue } = share.getInfoWithoutType(globalSnapshot, id);\n    const effectiveRemoteSnapshot = remoteSnapshot || snapshotValue;\n    if (effectiveRemoteSnapshot && !sdk.isManifestProvider(effectiveRemoteSnapshot)) {\n        traverse(effectiveRemoteSnapshot, remoteInfo, isRoot);\n        if (effectiveRemoteSnapshot.remotesInfo) {\n            const remoteKeys = Object.keys(effectiveRemoteSnapshot.remotesInfo);\n            for (const key of remoteKeys){\n                if (memo[key]) {\n                    continue;\n                }\n                memo[key] = true;\n                const subRemoteInfo = splitId(key);\n                const remoteValue = effectiveRemoteSnapshot.remotesInfo[key];\n                traverseModuleInfo(globalSnapshot, {\n                    name: subRemoteInfo.name,\n                    version: remoteValue.matchedVersion\n                }, traverse, false, memo, undefined);\n            }\n        }\n    }\n}\n// eslint-disable-next-line max-lines-per-function\nfunction generatePreloadAssets(origin, preloadOptions, remote, globalSnapshot, remoteSnapshot) {\n    const cssAssets = [];\n    const jsAssets = [];\n    const entryAssets = [];\n    const loadedSharedJsAssets = new Set();\n    const loadedSharedCssAssets = new Set();\n    const { options } = origin;\n    const { preloadConfig: rootPreloadConfig } = preloadOptions;\n    const { depsRemote } = rootPreloadConfig;\n    const memo = {};\n    traverseModuleInfo(globalSnapshot, remote, (moduleInfoSnapshot, remoteInfo, isRoot)=>{\n        let preloadConfig;\n        if (isRoot) {\n            preloadConfig = rootPreloadConfig;\n        } else {\n            if (Array.isArray(depsRemote)) {\n                // eslint-disable-next-line array-callback-return\n                const findPreloadConfig = depsRemote.find((remoteConfig)=>{\n                    if (remoteConfig.nameOrAlias === remoteInfo.name || remoteConfig.nameOrAlias === remoteInfo.alias) {\n                        return true;\n                    }\n                    return false;\n                });\n                if (!findPreloadConfig) {\n                    return;\n                }\n                preloadConfig = defaultPreloadArgs(findPreloadConfig);\n            } else if (depsRemote === true) {\n                preloadConfig = rootPreloadConfig;\n            } else {\n                return;\n            }\n        }\n        const remoteEntryUrl = sdk.getResourceUrl(moduleInfoSnapshot, share.getRemoteEntryInfoFromSnapshot(moduleInfoSnapshot).url);\n        if (remoteEntryUrl) {\n            entryAssets.push({\n                name: remoteInfo.name,\n                moduleInfo: {\n                    name: remoteInfo.name,\n                    entry: remoteEntryUrl,\n                    type: 'remoteEntryType' in moduleInfoSnapshot ? moduleInfoSnapshot.remoteEntryType : 'global',\n                    entryGlobalName: 'globalName' in moduleInfoSnapshot ? moduleInfoSnapshot.globalName : remoteInfo.name,\n                    shareScope: '',\n                    version: 'version' in moduleInfoSnapshot ? moduleInfoSnapshot.version : undefined\n                },\n                url: remoteEntryUrl\n            });\n        }\n        let moduleAssetsInfo = 'modules' in moduleInfoSnapshot ? moduleInfoSnapshot.modules : [];\n        const normalizedPreloadExposes = normalizePreloadExposes(preloadConfig.exposes);\n        if (normalizedPreloadExposes.length && 'modules' in moduleInfoSnapshot) {\n            var _moduleInfoSnapshot_modules;\n            moduleAssetsInfo = moduleInfoSnapshot == null ? void 0 : (_moduleInfoSnapshot_modules = moduleInfoSnapshot.modules) == null ? void 0 : _moduleInfoSnapshot_modules.reduce((assets, moduleAssetInfo)=>{\n                if ((normalizedPreloadExposes == null ? void 0 : normalizedPreloadExposes.indexOf(moduleAssetInfo.moduleName)) !== -1) {\n                    assets.push(moduleAssetInfo);\n                }\n                return assets;\n            }, []);\n        }\n        function handleAssets(assets) {\n            const assetsRes = assets.map((asset)=>sdk.getResourceUrl(moduleInfoSnapshot, asset));\n            if (preloadConfig.filter) {\n                return assetsRes.filter(preloadConfig.filter);\n            }\n            return assetsRes;\n        }\n        if (moduleAssetsInfo) {\n            const assetsLength = moduleAssetsInfo.length;\n            for(let index = 0; index < assetsLength; index++){\n                const assetsInfo = moduleAssetsInfo[index];\n                const exposeFullPath = `${remoteInfo.name}/${assetsInfo.moduleName}`;\n                origin.remoteHandler.hooks.lifecycle.handlePreloadModule.emit({\n                    id: assetsInfo.moduleName === '.' ? remoteInfo.name : exposeFullPath,\n                    name: remoteInfo.name,\n                    remoteSnapshot: moduleInfoSnapshot,\n                    preloadConfig,\n                    remote: remoteInfo,\n                    origin\n                });\n                const preloaded = share.getPreloaded(exposeFullPath);\n                if (preloaded) {\n                    continue;\n                }\n                if (preloadConfig.resourceCategory === 'all') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.async));\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.async));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                // eslint-disable-next-line no-constant-condition\n                } else if (preloadConfig.resourceCategory = 'sync') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                }\n                share.setPreloaded(exposeFullPath);\n            }\n        }\n    }, true, memo, remoteSnapshot);\n    if (remoteSnapshot.shared) {\n        const collectSharedAssets = (shareInfo, snapshotShared)=>{\n            const registeredShared = share.getRegisteredShare(origin.shareScopeMap, snapshotShared.sharedName, shareInfo, origin.sharedHandler.hooks.lifecycle.resolveShare);\n            // If the global share does not exist, or the lib function does not exist, it means that the shared has not been loaded yet and can be preloaded.\n            if (registeredShared && typeof registeredShared.lib === 'function') {\n                snapshotShared.assets.js.sync.forEach((asset)=>{\n                    loadedSharedJsAssets.add(asset);\n                });\n                snapshotShared.assets.css.sync.forEach((asset)=>{\n                    loadedSharedCssAssets.add(asset);\n                });\n            }\n        };\n        remoteSnapshot.shared.forEach((shared)=>{\n            var _options_shared;\n            const shareInfos = (_options_shared = options.shared) == null ? void 0 : _options_shared[shared.sharedName];\n            if (!shareInfos) {\n                return;\n            }\n            // if no version, preload all shared\n            const sharedOptions = shared.version ? shareInfos.find((s)=>s.version === shared.version) : shareInfos;\n            if (!sharedOptions) {\n                return;\n            }\n            const arrayShareInfo = share.arrayOptions(sharedOptions);\n            arrayShareInfo.forEach((s)=>{\n                collectSharedAssets(s, shared);\n            });\n        });\n    }\n    const needPreloadJsAssets = jsAssets.filter((asset)=>!loadedSharedJsAssets.has(asset));\n    const needPreloadCssAssets = cssAssets.filter((asset)=>!loadedSharedCssAssets.has(asset));\n    return {\n        cssAssets: needPreloadCssAssets,\n        jsAssetsWithoutEntry: needPreloadJsAssets,\n        entryAssets\n    };\n}\nconst generatePreloadAssetsPlugin = function() {\n    return {\n        name: 'generate-preload-assets-plugin',\n        async generatePreloadAssets (args) {\n            const { origin, preloadOptions, remoteInfo, remote, globalSnapshot, remoteSnapshot } = args;\n            if (share.isRemoteInfoWithEntry(remote) && share.isPureRemoteEntry(remote)) {\n                return {\n                    cssAssets: [],\n                    jsAssetsWithoutEntry: [],\n                    entryAssets: [\n                        {\n                            name: remote.name,\n                            url: remote.entry,\n                            moduleInfo: {\n                                name: remoteInfo.name,\n                                entry: remote.entry,\n                                type: remoteInfo.type || 'global',\n                                entryGlobalName: '',\n                                shareScope: ''\n                            }\n                        }\n                    ]\n                };\n            }\n            assignRemoteInfo(remoteInfo, remoteSnapshot);\n            const assets = generatePreloadAssets(origin, preloadOptions, remoteInfo, globalSnapshot, remoteSnapshot);\n            return assets;\n        }\n    };\n};\n\nfunction getGlobalRemoteInfo(moduleInfo, origin) {\n    const hostGlobalSnapshot = share.getGlobalSnapshotInfoByModuleInfo({\n        name: origin.options.name,\n        version: origin.options.version\n    });\n    // get remote detail info from global\n    const globalRemoteInfo = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && share.getInfoWithoutType(hostGlobalSnapshot.remotesInfo, moduleInfo.name).value;\n    if (globalRemoteInfo && globalRemoteInfo.matchedVersion) {\n        return {\n            hostGlobalSnapshot,\n            globalSnapshot: share.getGlobalSnapshot(),\n            remoteSnapshot: share.getGlobalSnapshotInfoByModuleInfo({\n                name: moduleInfo.name,\n                version: globalRemoteInfo.matchedVersion\n            })\n        };\n    }\n    return {\n        hostGlobalSnapshot: undefined,\n        globalSnapshot: share.getGlobalSnapshot(),\n        remoteSnapshot: share.getGlobalSnapshotInfoByModuleInfo({\n            name: moduleInfo.name,\n            version: 'version' in moduleInfo ? moduleInfo.version : undefined\n        })\n    };\n}\nclass SnapshotHandler {\n    async loadSnapshot(moduleInfo) {\n        const { options } = this.HostInstance;\n        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);\n        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({\n            options,\n            moduleInfo,\n            hostGlobalSnapshot,\n            remoteSnapshot,\n            globalSnapshot\n        });\n        return {\n            remoteSnapshot: globalRemoteSnapshot,\n            globalSnapshot: globalSnapshotRes\n        };\n    }\n    // eslint-disable-next-line max-lines-per-function\n    async loadRemoteSnapshotInfo(moduleInfo) {\n        const { options } = this.HostInstance;\n        await this.hooks.lifecycle.beforeLoadRemoteSnapshot.emit({\n            options,\n            moduleInfo\n        });\n        let hostSnapshot = share.getGlobalSnapshotInfoByModuleInfo({\n            name: this.HostInstance.options.name,\n            version: this.HostInstance.options.version\n        });\n        if (!hostSnapshot) {\n            hostSnapshot = {\n                version: this.HostInstance.options.version || '',\n                remoteEntry: '',\n                remotesInfo: {}\n            };\n            share.addGlobalSnapshot({\n                [this.HostInstance.options.name]: hostSnapshot\n            });\n        }\n        // In dynamic loadRemote scenarios, incomplete remotesInfo delivery may occur. In such cases, the remotesInfo in the host needs to be completed in the snapshot at runtime.\n        // This ensures the snapshot's integrity and helps the chrome plugin correctly identify all producer modules, ensuring that proxyable producer modules will not be missing.\n        if (hostSnapshot && 'remotesInfo' in hostSnapshot && !share.getInfoWithoutType(hostSnapshot.remotesInfo, moduleInfo.name).value) {\n            if ('version' in moduleInfo || 'entry' in moduleInfo) {\n                hostSnapshot.remotesInfo = polyfills._extends({}, hostSnapshot == null ? void 0 : hostSnapshot.remotesInfo, {\n                    [moduleInfo.name]: {\n                        matchedVersion: 'version' in moduleInfo ? moduleInfo.version : moduleInfo.entry\n                    }\n                });\n            }\n        }\n        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);\n        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({\n            options,\n            moduleInfo,\n            hostGlobalSnapshot,\n            remoteSnapshot,\n            globalSnapshot\n        });\n        // global snapshot includes manifest or module info includes manifest\n        if (globalRemoteSnapshot) {\n            if (sdk.isManifestProvider(globalRemoteSnapshot)) {\n                const remoteEntry = sdk.isBrowserEnv() ? globalRemoteSnapshot.remoteEntry : globalRemoteSnapshot.ssrRemoteEntry || globalRemoteSnapshot.remoteEntry || '';\n                const moduleSnapshot = await this.getManifestJson(remoteEntry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = share.setGlobalSnapshotInfoByModuleInfo(polyfills._extends({}, moduleInfo, {\n                    // The global remote may be overridden\n                    // Therefore, set the snapshot key to the global address of the actual request\n                    entry: remoteEntry\n                }), moduleSnapshot);\n                return {\n                    remoteSnapshot: moduleSnapshot,\n                    globalSnapshot: globalSnapshotRes\n                };\n            } else {\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: globalRemoteSnapshot,\n                    from: 'global'\n                });\n                return {\n                    remoteSnapshot: remoteSnapshotRes,\n                    globalSnapshot: globalSnapshotRes\n                };\n            }\n        } else {\n            if (share.isRemoteInfoWithEntry(moduleInfo)) {\n                // get from manifest.json and merge remote info from remote server\n                const moduleSnapshot = await this.getManifestJson(moduleInfo.entry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = share.setGlobalSnapshotInfoByModuleInfo(moduleInfo, moduleSnapshot);\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: moduleSnapshot,\n                    from: 'global'\n                });\n                return {\n                    remoteSnapshot: remoteSnapshotRes,\n                    globalSnapshot: globalSnapshotRes\n                };\n            } else {\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_007, errorCodes.runtimeDescMap, {\n                    hostName: moduleInfo.name,\n                    hostVersion: moduleInfo.version,\n                    globalSnapshot: JSON.stringify(globalSnapshotRes)\n                }));\n            }\n        }\n    }\n    getGlobalRemoteInfo(moduleInfo) {\n        return getGlobalRemoteInfo(moduleInfo, this.HostInstance);\n    }\n    async getManifestJson(manifestUrl, moduleInfo, extraOptions) {\n        const getManifest = async ()=>{\n            let manifestJson = this.manifestCache.get(manifestUrl);\n            if (manifestJson) {\n                return manifestJson;\n            }\n            try {\n                let res = await this.loaderHook.lifecycle.fetch.emit(manifestUrl, {});\n                if (!res || !(res instanceof Response)) {\n                    res = await fetch(manifestUrl, {});\n                }\n                manifestJson = await res.json();\n                share.assert(manifestJson.metaData && manifestJson.exposes && manifestJson.shared, `${manifestUrl} is not a federation manifest`);\n                this.manifestCache.set(manifestUrl, manifestJson);\n                return manifestJson;\n            } catch (err) {\n                delete this.manifestLoading[manifestUrl];\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_003, errorCodes.runtimeDescMap, {\n                    manifestUrl,\n                    moduleName: moduleInfo.name\n                }, `${err}`));\n            }\n        };\n        const asyncLoadProcess = async ()=>{\n            const manifestJson = await getManifest();\n            const remoteSnapshot = sdk.generateSnapshotFromManifest(manifestJson, {\n                version: manifestUrl\n            });\n            const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                options: this.HostInstance.options,\n                moduleInfo,\n                manifestJson,\n                remoteSnapshot,\n                manifestUrl,\n                from: 'manifest'\n            });\n            return remoteSnapshotRes;\n        };\n        if (!this.manifestLoading[manifestUrl]) {\n            this.manifestLoading[manifestUrl] = asyncLoadProcess().then((res)=>res);\n        }\n        return this.manifestLoading[manifestUrl];\n    }\n    constructor(HostInstance){\n        this.loadingHostSnapshot = null;\n        this.manifestCache = new Map();\n        this.hooks = new PluginSystem({\n            beforeLoadRemoteSnapshot: new AsyncHook('beforeLoadRemoteSnapshot'),\n            loadSnapshot: new AsyncWaterfallHook('loadGlobalSnapshot'),\n            loadRemoteSnapshot: new AsyncWaterfallHook('loadRemoteSnapshot')\n        });\n        this.manifestLoading = share.Global.__FEDERATION__.__MANIFEST_LOADING__;\n        this.HostInstance = HostInstance;\n        this.loaderHook = HostInstance.loaderHook;\n    }\n}\n\nclass SharedHandler {\n    // register shared in shareScopeMap\n    registerShared(globalOptions, userOptions) {\n        const { shareInfos, shared } = share.formatShareConfigs(globalOptions, userOptions);\n        const sharedKeys = Object.keys(shareInfos);\n        sharedKeys.forEach((sharedKey)=>{\n            const sharedVals = shareInfos[sharedKey];\n            sharedVals.forEach((sharedVal)=>{\n                const registeredShared = share.getRegisteredShare(this.shareScopeMap, sharedKey, sharedVal, this.hooks.lifecycle.resolveShare);\n                if (!registeredShared && sharedVal && sharedVal.lib) {\n                    this.setShared({\n                        pkgName: sharedKey,\n                        lib: sharedVal.lib,\n                        get: sharedVal.get,\n                        loaded: true,\n                        shared: sharedVal,\n                        from: userOptions.name\n                    });\n                }\n            });\n        });\n        return {\n            shareInfos,\n            shared\n        };\n    }\n    async loadShare(pkgName, extraOptions) {\n        const { host } = this;\n        // This function performs the following steps:\n        // 1. Checks if the currently loaded share already exists, if not, it throws an error\n        // 2. Searches globally for a matching share, if found, it uses it directly\n        // 3. If not found, it retrieves it from the current share and stores the obtained share globally.\n        const shareInfo = share.getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            await Promise.all(shareInfo.scope.map(async (shareScope)=>{\n                await Promise.all(this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                }));\n                return;\n            }));\n        }\n        const loadShareRes = await this.hooks.lifecycle.beforeLoadShare.emit({\n            pkgName,\n            shareInfo,\n            shared: host.options.shared,\n            origin: host\n        });\n        const { shareInfo: shareInfoRes } = loadShareRes;\n        // Assert that shareInfoRes exists, if not, throw an error\n        share.assert(shareInfoRes, `Cannot find ${pkgName} Share in the ${host.options.name}. Please ensure that the ${pkgName} Share parameters have been injected`);\n        // Retrieve from cache\n        const registeredShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            share.addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared && registeredShared.lib) {\n            addUseIn(registeredShared);\n            return registeredShared.lib;\n        } else if (registeredShared && registeredShared.loading && !registeredShared.loaded) {\n            const factory = await registeredShared.loading;\n            registeredShared.loaded = true;\n            if (!registeredShared.lib) {\n                registeredShared.lib = factory;\n            }\n            addUseIn(registeredShared);\n            return factory;\n        } else if (registeredShared) {\n            const asyncLoadProcess = async ()=>{\n                const factory = await registeredShared.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: registeredShared,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        } else {\n            if (extraOptions == null ? void 0 : extraOptions.customShareInfo) {\n                return false;\n            }\n            const asyncLoadProcess = async ()=>{\n                const factory = await shareInfoRes.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: shareInfoRes,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        }\n    }\n    /**\n   * This function initializes the sharing sequence (executed only once per share scope).\n   * It accepts one argument, the name of the share scope.\n   * If the share scope does not exist, it creates one.\n   */ // eslint-disable-next-line @typescript-eslint/member-ordering\n    initializeSharing(shareScopeName = share.DEFAULT_SCOPE, extraOptions) {\n        const { host } = this;\n        const from = extraOptions == null ? void 0 : extraOptions.from;\n        const strategy = extraOptions == null ? void 0 : extraOptions.strategy;\n        let initScope = extraOptions == null ? void 0 : extraOptions.initScope;\n        const promises = [];\n        if (from !== 'build') {\n            const { initTokens } = this;\n            if (!initScope) initScope = [];\n            let initToken = initTokens[shareScopeName];\n            if (!initToken) initToken = initTokens[shareScopeName] = {\n                from: this.host.name\n            };\n            if (initScope.indexOf(initToken) >= 0) return promises;\n            initScope.push(initToken);\n        }\n        const shareScope = this.shareScopeMap;\n        const hostName = host.options.name;\n        // Creates a new share scope if necessary\n        if (!shareScope[shareScopeName]) {\n            shareScope[shareScopeName] = {};\n        }\n        // Executes all initialization snippets from all accessible modules\n        const scope = shareScope[shareScopeName];\n        const register = (name, shared)=>{\n            var _activeVersion_shareConfig;\n            const { version, eager } = shared;\n            scope[name] = scope[name] || {};\n            const versions = scope[name];\n            const activeVersion = versions[version];\n            const activeVersionEager = Boolean(activeVersion && (activeVersion.eager || ((_activeVersion_shareConfig = activeVersion.shareConfig) == null ? void 0 : _activeVersion_shareConfig.eager)));\n            if (!activeVersion || activeVersion.strategy !== 'loaded-first' && !activeVersion.loaded && (Boolean(!eager) !== !activeVersionEager ? eager : hostName > activeVersion.from)) {\n                versions[version] = shared;\n            }\n        };\n        const initFn = (mod)=>mod && mod.init && mod.init(shareScope[shareScopeName], initScope);\n        const initRemoteModule = async (key)=>{\n            const { module } = await host.remoteHandler.getRemoteModuleAndOptions({\n                id: key\n            });\n            if (module.getEntry) {\n                let remoteEntryExports;\n                try {\n                    remoteEntryExports = await module.getEntry();\n                } catch (error) {\n                    remoteEntryExports = await host.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({\n                        id: key,\n                        error,\n                        from: 'runtime',\n                        lifecycle: 'beforeLoadShare',\n                        origin: host\n                    });\n                }\n                if (!module.inited) {\n                    await initFn(remoteEntryExports);\n                    module.inited = true;\n                }\n            }\n        };\n        Object.keys(host.options.shared).forEach((shareName)=>{\n            const sharedArr = host.options.shared[shareName];\n            sharedArr.forEach((shared)=>{\n                if (shared.scope.includes(shareScopeName)) {\n                    register(shareName, shared);\n                }\n            });\n        });\n        // TODO: strategy==='version-first' need to be removed in the future\n        if (host.options.shareStrategy === 'version-first' || strategy === 'version-first') {\n            host.options.remotes.forEach((remote)=>{\n                if (remote.shareScope === shareScopeName) {\n                    promises.push(initRemoteModule(remote.name));\n                }\n            });\n        }\n        return promises;\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        const { host } = this;\n        const shareInfo = share.getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            shareInfo.scope.forEach((shareScope)=>{\n                this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                });\n            });\n        }\n        const registeredShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfo, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            share.addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared) {\n            if (typeof registeredShared.lib === 'function') {\n                addUseIn(registeredShared);\n                if (!registeredShared.loaded) {\n                    registeredShared.loaded = true;\n                    if (registeredShared.from === host.options.name) {\n                        shareInfo.loaded = true;\n                    }\n                }\n                return registeredShared.lib;\n            }\n            if (typeof registeredShared.get === 'function') {\n                const module = registeredShared.get();\n                if (!(module instanceof Promise)) {\n                    addUseIn(registeredShared);\n                    this.setShared({\n                        pkgName,\n                        loaded: true,\n                        from: host.options.name,\n                        lib: module,\n                        shared: registeredShared\n                    });\n                    return module;\n                }\n            }\n        }\n        if (shareInfo.lib) {\n            if (!shareInfo.loaded) {\n                shareInfo.loaded = true;\n            }\n            return shareInfo.lib;\n        }\n        if (shareInfo.get) {\n            const module = shareInfo.get();\n            if (module instanceof Promise) {\n                const errorCode = (extraOptions == null ? void 0 : extraOptions.from) === 'build' ? errorCodes.RUNTIME_005 : errorCodes.RUNTIME_006;\n                throw new Error(errorCodes.getShortErrorMsg(errorCode, errorCodes.runtimeDescMap, {\n                    hostName: host.options.name,\n                    sharedPkgName: pkgName\n                }));\n            }\n            shareInfo.lib = module;\n            this.setShared({\n                pkgName,\n                loaded: true,\n                from: host.options.name,\n                lib: shareInfo.lib,\n                shared: shareInfo\n            });\n            return shareInfo.lib;\n        }\n        throw new Error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_006, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            sharedPkgName: pkgName\n        }));\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        const { host } = this;\n        this.shareScopeMap[scopeName] = shareScope;\n        this.hooks.lifecycle.initContainerShareScopeMap.emit({\n            shareScope,\n            options: host.options,\n            origin: host,\n            scopeName,\n            hostShareScopeMap: extraOptions.hostShareScopeMap\n        });\n    }\n    setShared({ pkgName, shared, from, lib, loading, loaded, get }) {\n        const { version, scope = 'default' } = shared, shareInfo = polyfills._object_without_properties_loose(shared, [\n            \"version\",\n            \"scope\"\n        ]);\n        const scopes = Array.isArray(scope) ? scope : [\n            scope\n        ];\n        scopes.forEach((sc)=>{\n            if (!this.shareScopeMap[sc]) {\n                this.shareScopeMap[sc] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName]) {\n                this.shareScopeMap[sc][pkgName] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName][version]) {\n                this.shareScopeMap[sc][pkgName][version] = polyfills._extends({\n                    version,\n                    scope: [\n                        'default'\n                    ]\n                }, shareInfo, {\n                    lib,\n                    loaded,\n                    loading\n                });\n                if (get) {\n                    this.shareScopeMap[sc][pkgName][version].get = get;\n                }\n                return;\n            }\n            const registeredShared = this.shareScopeMap[sc][pkgName][version];\n            if (loading && !registeredShared.loading) {\n                registeredShared.loading = loading;\n            }\n        });\n    }\n    _setGlobalShareScopeMap(hostOptions) {\n        const globalShareScopeMap = share.getGlobalShareScope();\n        const identifier = hostOptions.id || hostOptions.name;\n        if (identifier && !globalShareScopeMap[identifier]) {\n            globalShareScopeMap[identifier] = this.shareScopeMap;\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            afterResolve: new AsyncWaterfallHook('afterResolve'),\n            beforeLoadShare: new AsyncWaterfallHook('beforeLoadShare'),\n            // not used yet\n            loadShare: new AsyncHook(),\n            resolveShare: new SyncWaterfallHook('resolveShare'),\n            // maybe will change, temporarily for internal use only\n            initContainerShareScopeMap: new SyncWaterfallHook('initContainerShareScopeMap')\n        });\n        this.host = host;\n        this.shareScopeMap = {};\n        this.initTokens = {};\n        this._setGlobalShareScopeMap(host.options);\n    }\n}\n\nclass RemoteHandler {\n    formatAndRegisterRemote(globalOptions, userOptions) {\n        const userRemotes = userOptions.remotes || [];\n        return userRemotes.reduce((res, remote)=>{\n            this.registerRemote(remote, res, {\n                force: false\n            });\n            return res;\n        }, globalOptions.remotes);\n    }\n    setIdToRemoteMap(id, remoteMatchInfo) {\n        const { remote, expose } = remoteMatchInfo;\n        const { name, alias } = remote;\n        this.idToRemoteMap[id] = {\n            name: remote.name,\n            expose\n        };\n        if (alias && id.startsWith(name)) {\n            const idWithAlias = id.replace(name, alias);\n            this.idToRemoteMap[idWithAlias] = {\n                name: remote.name,\n                expose\n            };\n            return;\n        }\n        if (alias && id.startsWith(alias)) {\n            const idWithName = id.replace(alias, name);\n            this.idToRemoteMap[idWithName] = {\n                name: remote.name,\n                expose\n            };\n        }\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        const { host } = this;\n        try {\n            const { loadFactory = true } = options || {\n                loadFactory: true\n            };\n            // 1. Validate the parameters of the retrieved module. There are two module request methods: pkgName + expose and alias + expose.\n            // 2. Request the snapshot information of the current host and globally store the obtained snapshot information. The retrieved module information is partially offline and partially online. The online module information will retrieve the modules used online.\n            // 3. Retrieve the detailed information of the current module from global (remoteEntry address, expose resource address)\n            // 4. After retrieving remoteEntry, call the init of the module, and then retrieve the exported content of the module through get\n            // id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n            // id: alias(app1) + expose(button) = app1/button\n            // id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\n            const { module, moduleOptions, remoteMatchInfo } = await this.getRemoteModuleAndOptions({\n                id\n            });\n            const { pkgNameOrAlias, remote, expose, id: idRes, remoteSnapshot } = remoteMatchInfo;\n            const moduleOrFactory = await module.get(idRes, expose, options, remoteSnapshot);\n            const moduleWrapper = await this.hooks.lifecycle.onLoad.emit({\n                id: idRes,\n                pkgNameOrAlias,\n                expose,\n                exposeModule: loadFactory ? moduleOrFactory : undefined,\n                exposeModuleFactory: loadFactory ? undefined : moduleOrFactory,\n                remote,\n                options: moduleOptions,\n                moduleInstance: module,\n                origin: host\n            });\n            this.setIdToRemoteMap(id, remoteMatchInfo);\n            if (typeof moduleWrapper === 'function') {\n                return moduleWrapper;\n            }\n            return moduleOrFactory;\n        } catch (error) {\n            const { from = 'runtime' } = options || {\n                from: 'runtime'\n            };\n            const failOver = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                error,\n                from,\n                lifecycle: 'onLoad',\n                origin: host\n            });\n            if (!failOver) {\n                throw error;\n            }\n            return failOver;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        const { host } = this;\n        await this.hooks.lifecycle.beforePreloadRemote.emit({\n            preloadOps: preloadOptions,\n            options: host.options,\n            origin: host\n        });\n        const preloadOps = formatPreloadArgs(host.options.remotes, preloadOptions);\n        await Promise.all(preloadOps.map(async (ops)=>{\n            const { remote } = ops;\n            const remoteInfo = getRemoteInfo(remote);\n            const { globalSnapshot, remoteSnapshot } = await host.snapshotHandler.loadRemoteSnapshotInfo(remote);\n            const assets = await this.hooks.lifecycle.generatePreloadAssets.emit({\n                origin: host,\n                preloadOptions: ops,\n                remote,\n                remoteInfo,\n                globalSnapshot,\n                remoteSnapshot\n            });\n            if (!assets) {\n                return;\n            }\n            preloadAssets(remoteInfo, host, assets);\n        }));\n    }\n    registerRemotes(remotes, options) {\n        const { host } = this;\n        remotes.forEach((remote)=>{\n            this.registerRemote(remote, host.options.remotes, {\n                force: options == null ? void 0 : options.force\n            });\n        });\n    }\n    async getRemoteModuleAndOptions(options) {\n        const { host } = this;\n        const { id } = options;\n        let loadRemoteArgs;\n        try {\n            loadRemoteArgs = await this.hooks.lifecycle.beforeRequest.emit({\n                id,\n                options: host.options,\n                origin: host\n            });\n        } catch (error) {\n            loadRemoteArgs = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                options: host.options,\n                origin: host,\n                from: 'runtime',\n                error,\n                lifecycle: 'beforeRequest'\n            });\n            if (!loadRemoteArgs) {\n                throw error;\n            }\n        }\n        const { id: idRes } = loadRemoteArgs;\n        const remoteSplitInfo = matchRemoteWithNameAndExpose(host.options.remotes, idRes);\n        share.assert(remoteSplitInfo, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_004, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            requestId: idRes\n        }));\n        const { remote: rawRemote } = remoteSplitInfo;\n        const remoteInfo = getRemoteInfo(rawRemote);\n        const matchInfo = await host.sharedHandler.hooks.lifecycle.afterResolve.emit(polyfills._extends({\n            id: idRes\n        }, remoteSplitInfo, {\n            options: host.options,\n            origin: host,\n            remoteInfo\n        }));\n        const { remote, expose } = matchInfo;\n        share.assert(remote && expose, `The 'beforeRequest' hook was executed, but it failed to return the correct 'remote' and 'expose' values while loading ${idRes}.`);\n        let module = host.moduleCache.get(remote.name);\n        const moduleOptions = {\n            host: host,\n            remoteInfo\n        };\n        if (!module) {\n            module = new Module(moduleOptions);\n            host.moduleCache.set(remote.name, module);\n        }\n        return {\n            module,\n            moduleOptions,\n            remoteMatchInfo: matchInfo\n        };\n    }\n    registerRemote(remote, targetRemotes, options) {\n        const { host } = this;\n        const normalizeRemote = ()=>{\n            if (remote.alias) {\n                // Validate if alias equals the prefix of remote.name and remote.alias, if so, throw an error\n                // As multi-level path references cannot guarantee unique names, alias being a prefix of remote.name is not supported\n                const findEqual = targetRemotes.find((item)=>{\n                    var _item_alias;\n                    return remote.alias && (item.name.startsWith(remote.alias) || ((_item_alias = item.alias) == null ? void 0 : _item_alias.startsWith(remote.alias)));\n                });\n                share.assert(!findEqual, `The alias ${remote.alias} of remote ${remote.name} is not allowed to be the prefix of ${findEqual && findEqual.name} name or alias`);\n            }\n            // Set the remote entry to a complete path\n            if ('entry' in remote) {\n                if (sdk.isBrowserEnv() && !remote.entry.startsWith('http')) {\n                    remote.entry = new URL(remote.entry, window.location.origin).href;\n                }\n            }\n            if (!remote.shareScope) {\n                remote.shareScope = share.DEFAULT_SCOPE;\n            }\n            if (!remote.type) {\n                remote.type = share.DEFAULT_REMOTE_TYPE;\n            }\n        };\n        this.hooks.lifecycle.beforeRegisterRemote.emit({\n            remote,\n            origin: host\n        });\n        const registeredRemote = targetRemotes.find((item)=>item.name === remote.name);\n        if (!registeredRemote) {\n            normalizeRemote();\n            targetRemotes.push(remote);\n            this.hooks.lifecycle.registerRemote.emit({\n                remote,\n                origin: host\n            });\n        } else {\n            const messages = [\n                `The remote \"${remote.name}\" is already registered.`,\n                (options == null ? void 0 : options.force) ? 'Hope you have known that OVERRIDE it may have some unexpected errors' : 'If you want to merge the remote, you can set \"force: true\".'\n            ];\n            if (options == null ? void 0 : options.force) {\n                // remove registered remote\n                this.removeRemote(registeredRemote);\n                normalizeRemote();\n                targetRemotes.push(remote);\n                this.hooks.lifecycle.registerRemote.emit({\n                    remote,\n                    origin: host\n                });\n            }\n            sdk.warn(messages.join(' '));\n        }\n    }\n    removeRemote(remote) {\n        try {\n            const { host } = this;\n            const { name } = remote;\n            const remoteIndex = host.options.remotes.findIndex((item)=>item.name === name);\n            if (remoteIndex !== -1) {\n                host.options.remotes.splice(remoteIndex, 1);\n            }\n            const loadedModule = host.moduleCache.get(remote.name);\n            if (loadedModule) {\n                const remoteInfo = loadedModule.remoteInfo;\n                const key = remoteInfo.entryGlobalName;\n                if (globalThis[key]) {\n                    var _Object_getOwnPropertyDescriptor;\n                    if ((_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(globalThis, key)) == null ? void 0 : _Object_getOwnPropertyDescriptor.configurable) {\n                        delete globalThis[key];\n                    } else {\n                        // @ts-ignore\n                        globalThis[key] = undefined;\n                    }\n                }\n                const remoteEntryUniqueKey = getRemoteEntryUniqueKey(loadedModule.remoteInfo);\n                if (share.globalLoading[remoteEntryUniqueKey]) {\n                    delete share.globalLoading[remoteEntryUniqueKey];\n                }\n                host.snapshotHandler.manifestCache.delete(remoteInfo.entry);\n                // delete unloaded shared and instance\n                let remoteInsId = remoteInfo.buildVersion ? sdk.composeKeyWithSeparator(remoteInfo.name, remoteInfo.buildVersion) : remoteInfo.name;\n                const remoteInsIndex = globalThis.__FEDERATION__.__INSTANCES__.findIndex((ins)=>{\n                    if (remoteInfo.buildVersion) {\n                        return ins.options.id === remoteInsId;\n                    } else {\n                        return ins.name === remoteInsId;\n                    }\n                });\n                if (remoteInsIndex !== -1) {\n                    const remoteIns = globalThis.__FEDERATION__.__INSTANCES__[remoteInsIndex];\n                    remoteInsId = remoteIns.options.id || remoteInsId;\n                    const globalShareScopeMap = share.getGlobalShareScope();\n                    let isAllSharedNotUsed = true;\n                    const needDeleteKeys = [];\n                    Object.keys(globalShareScopeMap).forEach((instId)=>{\n                        const shareScopeMap = globalShareScopeMap[instId];\n                        shareScopeMap && Object.keys(shareScopeMap).forEach((shareScope)=>{\n                            const shareScopeVal = shareScopeMap[shareScope];\n                            shareScopeVal && Object.keys(shareScopeVal).forEach((shareName)=>{\n                                const sharedPkgs = shareScopeVal[shareName];\n                                sharedPkgs && Object.keys(sharedPkgs).forEach((shareVersion)=>{\n                                    const shared = sharedPkgs[shareVersion];\n                                    if (shared && typeof shared === 'object' && shared.from === remoteInfo.name) {\n                                        if (shared.loaded || shared.loading) {\n                                            shared.useIn = shared.useIn.filter((usedHostName)=>usedHostName !== remoteInfo.name);\n                                            if (shared.useIn.length) {\n                                                isAllSharedNotUsed = false;\n                                            } else {\n                                                needDeleteKeys.push([\n                                                    instId,\n                                                    shareScope,\n                                                    shareName,\n                                                    shareVersion\n                                                ]);\n                                            }\n                                        } else {\n                                            needDeleteKeys.push([\n                                                instId,\n                                                shareScope,\n                                                shareName,\n                                                shareVersion\n                                            ]);\n                                        }\n                                    }\n                                });\n                            });\n                        });\n                    });\n                    if (isAllSharedNotUsed) {\n                        remoteIns.shareScopeMap = {};\n                        delete globalShareScopeMap[remoteInsId];\n                    }\n                    needDeleteKeys.forEach(([insId, shareScope, shareName, shareVersion])=>{\n                        var _globalShareScopeMap_insId_shareScope_shareName, _globalShareScopeMap_insId_shareScope, _globalShareScopeMap_insId;\n                        (_globalShareScopeMap_insId = globalShareScopeMap[insId]) == null ? true : (_globalShareScopeMap_insId_shareScope = _globalShareScopeMap_insId[shareScope]) == null ? true : (_globalShareScopeMap_insId_shareScope_shareName = _globalShareScopeMap_insId_shareScope[shareName]) == null ? true : delete _globalShareScopeMap_insId_shareScope_shareName[shareVersion];\n                    });\n                    globalThis.__FEDERATION__.__INSTANCES__.splice(remoteInsIndex, 1);\n                }\n                const { hostGlobalSnapshot } = getGlobalRemoteInfo(remote, host);\n                if (hostGlobalSnapshot) {\n                    const remoteKey = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && share.getInfoWithoutType(hostGlobalSnapshot.remotesInfo, remote.name).key;\n                    if (remoteKey) {\n                        delete hostGlobalSnapshot.remotesInfo[remoteKey];\n                        if (//eslint-disable-next-line no-extra-boolean-cast\n                        Boolean(share.Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey])) {\n                            delete share.Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey];\n                        }\n                    }\n                }\n                host.moduleCache.delete(remote.name);\n            }\n        } catch (err) {\n            share.logger.log('removeRemote fail: ', err);\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            beforeRegisterRemote: new SyncWaterfallHook('beforeRegisterRemote'),\n            registerRemote: new SyncWaterfallHook('registerRemote'),\n            beforeRequest: new AsyncWaterfallHook('beforeRequest'),\n            onLoad: new AsyncHook('onLoad'),\n            handlePreloadModule: new SyncHook('handlePreloadModule'),\n            errorLoadRemote: new AsyncHook('errorLoadRemote'),\n            beforePreloadRemote: new AsyncHook('beforePreloadRemote'),\n            generatePreloadAssets: new AsyncHook('generatePreloadAssets'),\n            // not used yet\n            afterPreloadRemote: new AsyncHook(),\n            loadEntry: new AsyncHook()\n        });\n        this.host = host;\n        this.idToRemoteMap = {};\n    }\n}\n\nclass FederationHost {\n    initOptions(userOptions) {\n        this.registerPlugins(userOptions.plugins);\n        const options = this.formatOptions(this.options, userOptions);\n        this.options = options;\n        return options;\n    }\n    async loadShare(pkgName, extraOptions) {\n        return this.sharedHandler.loadShare(pkgName, extraOptions);\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        return this.sharedHandler.loadShareSync(pkgName, extraOptions);\n    }\n    initializeSharing(shareScopeName = share.DEFAULT_SCOPE, extraOptions) {\n        return this.sharedHandler.initializeSharing(shareScopeName, extraOptions);\n    }\n    initRawContainer(name, url, container) {\n        const remoteInfo = getRemoteInfo({\n            name,\n            entry: url\n        });\n        const module = new Module({\n            host: this,\n            remoteInfo\n        });\n        module.remoteEntryExports = container;\n        this.moduleCache.set(name, module);\n        return module;\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        return this.remoteHandler.loadRemote(id, options);\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        return this.remoteHandler.preloadRemote(preloadOptions);\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        this.sharedHandler.initShareScopeMap(scopeName, shareScope, extraOptions);\n    }\n    formatOptions(globalOptions, userOptions) {\n        const { shared } = share.formatShareConfigs(globalOptions, userOptions);\n        const { userOptions: userOptionsRes, options: globalOptionsRes } = this.hooks.lifecycle.beforeInit.emit({\n            origin: this,\n            userOptions,\n            options: globalOptions,\n            shareInfo: shared\n        });\n        const remotes = this.remoteHandler.formatAndRegisterRemote(globalOptionsRes, userOptionsRes);\n        const { shared: handledShared } = this.sharedHandler.registerShared(globalOptionsRes, userOptionsRes);\n        const plugins = [\n            ...globalOptionsRes.plugins\n        ];\n        if (userOptionsRes.plugins) {\n            userOptionsRes.plugins.forEach((plugin)=>{\n                if (!plugins.includes(plugin)) {\n                    plugins.push(plugin);\n                }\n            });\n        }\n        const optionsRes = polyfills._extends({}, globalOptions, userOptions, {\n            plugins,\n            remotes,\n            shared: handledShared\n        });\n        this.hooks.lifecycle.init.emit({\n            origin: this,\n            options: optionsRes\n        });\n        return optionsRes;\n    }\n    registerPlugins(plugins) {\n        const pluginRes = registerPlugins$1(plugins, [\n            this.hooks,\n            this.remoteHandler.hooks,\n            this.sharedHandler.hooks,\n            this.snapshotHandler.hooks,\n            this.loaderHook\n        ]);\n        // Merge plugin\n        this.options.plugins = this.options.plugins.reduce((res, plugin)=>{\n            if (!plugin) return res;\n            if (res && !res.find((item)=>item.name === plugin.name)) {\n                res.push(plugin);\n            }\n            return res;\n        }, pluginRes || []);\n    }\n    registerRemotes(remotes, options) {\n        return this.remoteHandler.registerRemotes(remotes, options);\n    }\n    constructor(userOptions){\n        this.hooks = new PluginSystem({\n            beforeInit: new SyncWaterfallHook('beforeInit'),\n            init: new SyncHook(),\n            // maybe will change, temporarily for internal use only\n            beforeInitContainer: new AsyncWaterfallHook('beforeInitContainer'),\n            // maybe will change, temporarily for internal use only\n            initContainer: new AsyncWaterfallHook('initContainer')\n        });\n        this.version = \"0.6.16\";\n        this.moduleCache = new Map();\n        this.loaderHook = new PluginSystem({\n            // FIXME: may not be suitable , not open to the public yet\n            getModuleInfo: new SyncHook(),\n            createScript: new SyncHook(),\n            createLink: new SyncHook(),\n            // only work for manifest , so not open to the public yet\n            fetch: new AsyncHook(),\n            getModuleFactory: new AsyncHook()\n        });\n        // TODO: Validate the details of the options\n        // Initialize options with default values\n        const defaultOptions = {\n            id: share.getBuilderId(),\n            name: userOptions.name,\n            plugins: [\n                snapshotPlugin(),\n                generatePreloadAssetsPlugin()\n            ],\n            remotes: [],\n            shared: {},\n            inBrowser: sdk.isBrowserEnv()\n        };\n        this.name = userOptions.name;\n        this.options = defaultOptions;\n        this.snapshotHandler = new SnapshotHandler(this);\n        this.sharedHandler = new SharedHandler(this);\n        this.remoteHandler = new RemoteHandler(this);\n        this.shareScopeMap = this.sharedHandler.shareScopeMap;\n        this.registerPlugins([\n            ...defaultOptions.plugins,\n            ...userOptions.plugins || []\n        ]);\n        this.options = this.formatOptions(defaultOptions, userOptions);\n    }\n}\n\nlet FederationInstance = null;\nfunction init(options) {\n    // Retrieve the same instance with the same name\n    const instance = share.getGlobalFederationInstance(options.name, options.version);\n    if (!instance) {\n        // Retrieve debug constructor\n        const FederationConstructor = share.getGlobalFederationConstructor() || FederationHost;\n        FederationInstance = new FederationConstructor(options);\n        share.setGlobalFederationInstance(FederationInstance);\n        return FederationInstance;\n    } else {\n        // Merge options\n        instance.initOptions(options);\n        if (!FederationInstance) {\n            FederationInstance = instance;\n        }\n        return instance;\n    }\n}\nfunction loadRemote(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    const loadRemote1 = FederationInstance.loadRemote;\n    // eslint-disable-next-line prefer-spread\n    return loadRemote1.apply(FederationInstance, args);\n}\nfunction loadShare(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    const loadShare1 = FederationInstance.loadShare;\n    return loadShare1.apply(FederationInstance, args);\n}\nfunction loadShareSync(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    const loadShareSync1 = FederationInstance.loadShareSync;\n    // eslint-disable-next-line prefer-spread\n    return loadShareSync1.apply(FederationInstance, args);\n}\nfunction preloadRemote(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.preloadRemote.apply(FederationInstance, args);\n}\nfunction registerRemotes(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerRemotes.apply(FederationInstance, args);\n}\nfunction registerPlugins(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerPlugins.apply(FederationInstance, args);\n}\nfunction getInstance() {\n    return FederationInstance;\n}\n// Inject for debug\nshare.setGlobalFederationConstructor(FederationHost);\n\nObject.defineProperty(exports, \"loadScript\", ({\n  enumerable: true,\n  get: function () { return sdk.loadScript; }\n}));\nObject.defineProperty(exports, \"loadScriptNode\", ({\n  enumerable: true,\n  get: function () { return sdk.loadScriptNode; }\n}));\nexports.registerGlobalPlugins = share.registerGlobalPlugins;\nexports.FederationHost = FederationHost;\nexports.Module = Module;\nexports.getInstance = getInstance;\nexports.getRemoteEntry = getRemoteEntry;\nexports.getRemoteInfo = getRemoteInfo;\nexports.init = init;\nexports.loadRemote = loadRemote;\nexports.loadShare = loadShare;\nexports.loadShareSync = loadShareSync;\nexports.preloadRemote = preloadRemote;\nexports.registerPlugins = registerPlugins;\nexports.registerRemotes = registerRemotes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3197\n\n}");

/***/ }),

/***/ 3767:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nconst RUNTIME_001 = 'RUNTIME-001';\nconst RUNTIME_002 = 'RUNTIME-002';\nconst RUNTIME_003 = 'RUNTIME-003';\nconst RUNTIME_004 = 'RUNTIME-004';\nconst RUNTIME_005 = 'RUNTIME-005';\nconst RUNTIME_006 = 'RUNTIME-006';\nconst RUNTIME_007 = 'RUNTIME-007';\nconst TYPE_001 = 'TYPE-001';\n\nconst getDocsUrl = (errorCode)=>{\n    const type = errorCode.split('-')[0].toLowerCase();\n    return `https://module-federation.io/guide/troubleshooting/${type}/${errorCode}`;\n};\nconst getShortErrorMsg = (errorCode, errorDescMap, args, originalErrorMsg)=>{\n    const msg = [\n        errorDescMap[errorCode]\n    ];\n    args && msg.push(`args: ${JSON.stringify(args)}`);\n    msg.push(getDocsUrl(errorCode));\n    originalErrorMsg && msg.push(`Original Error Message:\\n ${originalErrorMsg}`);\n    return msg.join('\\n');\n};\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nconst runtimeDescMap = {\n    [RUNTIME_001]: 'Failed to get remoteEntry exports.',\n    [RUNTIME_002]: 'The remote entry interface does not contain \"init\"',\n    [RUNTIME_003]: 'Failed to get manifest.',\n    [RUNTIME_004]: 'Failed to locate remote.',\n    [RUNTIME_005]: 'Invalid loadShareSync function call from bundler runtime',\n    [RUNTIME_006]: 'Invalid loadShareSync function call from runtime',\n    [RUNTIME_007]: 'Failed to get remote snapshot.'\n};\nconst typeDescMap = {\n    [TYPE_001]: 'Failed to generate type declaration.'\n};\nconst errorDescMap = _extends({}, runtimeDescMap, typeDescMap);\n\nexports.RUNTIME_001 = RUNTIME_001;\nexports.RUNTIME_002 = RUNTIME_002;\nexports.RUNTIME_003 = RUNTIME_003;\nexports.RUNTIME_004 = RUNTIME_004;\nexports.RUNTIME_005 = RUNTIME_005;\nexports.RUNTIME_006 = RUNTIME_006;\nexports.RUNTIME_007 = RUNTIME_007;\nexports.TYPE_001 = TYPE_001;\nexports.errorDescMap = errorDescMap;\nexports.getShortErrorMsg = getShortErrorMsg;\nexports.runtimeDescMap = runtimeDescMap;\nexports.typeDescMap = typeDescMap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3767\n\n}");

/***/ }),

/***/ 4179:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar sdk = __webpack_require__(9468);\n\nconst FEDERATION_SUPPORTED_TYPES = [\n    'script'\n];\n\nObject.defineProperty(exports, \"ENCODE_NAME_PREFIX\", ({\n\tenumerable: true,\n\tget: function () { return sdk.ENCODE_NAME_PREFIX; }\n}));\nexports.FEDERATION_SUPPORTED_TYPES = FEDERATION_SUPPORTED_TYPES;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDE3OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixVQUFVLG1CQUFPLENBQUMsSUFBd0I7O0FBRTFDO0FBQ0E7QUFDQTs7QUFFQSxzREFBcUQ7QUFDckQ7QUFDQSxvQkFBb0I7QUFDcEIsQ0FBQyxFQUFDO0FBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGNybS90cmFuc2NyaXB0LWFuZC1zdW1tYXJ5Ly4uLy4uL25vZGVfbW9kdWxlcy9AbW9kdWxlLWZlZGVyYXRpb24vd2VicGFjay1idW5kbGVyLXJ1bnRpbWUvZGlzdC9jb25zdGFudC5janMuanM/MTU4MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBzZGsgPSByZXF1aXJlKCdAbW9kdWxlLWZlZGVyYXRpb24vc2RrJyk7XG5cbmNvbnN0IEZFREVSQVRJT05fU1VQUE9SVEVEX1RZUEVTID0gW1xuICAgICdzY3JpcHQnXG5dO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJFTkNPREVfTkFNRV9QUkVGSVhcIiwge1xuXHRlbnVtZXJhYmxlOiB0cnVlLFxuXHRnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNkay5FTkNPREVfTkFNRV9QUkVGSVg7IH1cbn0pO1xuZXhwb3J0cy5GRURFUkFUSU9OX1NVUFBPUlRFRF9UWVBFUyA9IEZFREVSQVRJT05fU1VQUE9SVEVEX1RZUEVTO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///4179\n\n}");

/***/ }),

/***/ 4639:
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("{\n\n// Import bootstrap asynchronously to create proper async boundary for Module Federation\n__webpack_require__.e(/* import() */ 492).then(__webpack_require__.bind(__webpack_require__, 3492)).then(function (_ref) {\n  var bootstrap = _ref.bootstrap;\n  bootstrap({\n    container: 'root'\n  })[\"catch\"](function (error) {\n    console.error('Failed to bootstrap Transcript and Summary app:', error);\n  });\n})[\"catch\"](function (error) {\n  console.error('Failed to load bootstrap module:', error);\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDYzOS5qcyIsIm1hcHBpbmdzIjoiOztBQUFBO0FBQ0EsbUdBQXFCLENBQUNBLElBQUksQ0FBQyxVQUFBQyxJQUFBLEVBQWtCO0VBQUEsSUFBZkMsU0FBUyxHQUFBRCxJQUFBLENBQVRDLFNBQVM7RUFDckNBLFNBQVMsQ0FBQztJQUNSQyxTQUFTLEVBQUU7R0FDWixDQUFDLFNBQU0sQ0FBQyxVQUFBQyxLQUFLLEVBQUc7SUFDZkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsaURBQWlELEVBQUVBLEtBQUssQ0FBQztFQUN6RSxDQUFDLENBQUM7QUFDSixDQUFDLENBQUMsU0FBTSxDQUFDLFVBQUFBLEtBQUssRUFBRztFQUNmQyxPQUFPLENBQUNELEtBQUssQ0FBQyxrQ0FBa0MsRUFBRUEsS0FBSyxDQUFDO0FBQzFELENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0Bjcm0vdHJhbnNjcmlwdC1hbmQtc3VtbWFyeS8uL3NyYy9tYWluLnRzeD80MmEwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydCBib290c3RyYXAgYXN5bmNocm9ub3VzbHkgdG8gY3JlYXRlIHByb3BlciBhc3luYyBib3VuZGFyeSBmb3IgTW9kdWxlIEZlZGVyYXRpb25cbmltcG9ydCgnLi9ib290c3RyYXAnKS50aGVuKCh7IGJvb3RzdHJhcCB9KSA9PiB7XG4gIGJvb3RzdHJhcCh7XG4gICAgY29udGFpbmVyOiAncm9vdCdcbiAgfSkuY2F0Y2goZXJyb3IgPT4ge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBib290c3RyYXAgVHJhbnNjcmlwdCBhbmQgU3VtbWFyeSBhcHA6JywgZXJyb3IpO1xuICB9KTtcbn0pLmNhdGNoKGVycm9yID0+IHtcbiAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgYm9vdHN0cmFwIG1vZHVsZTonLCBlcnJvcik7XG59KTtcbiJdLCJuYW1lcyI6WyJ0aGVuIiwiX3JlZiIsImJvb3RzdHJhcCIsImNvbnRhaW5lciIsImVycm9yIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///4639\n\n}");

/***/ }),

/***/ 6689:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nfunction _object_without_properties_loose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\nexports._extends = _extends;\nexports._object_without_properties_loose = _object_without_properties_loose;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjY4OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSx1QkFBdUI7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdCQUFnQjtBQUNoQix3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL3RyYW5zY3JpcHQtYW5kLXN1bW1hcnkvLi4vLi4vbm9kZV9tb2R1bGVzL0Btb2R1bGUtZmVkZXJhdGlvbi9ydW50aW1lLXRvb2xzL25vZGVfbW9kdWxlcy9AbW9kdWxlLWZlZGVyYXRpb24vcnVudGltZS9kaXN0L3BvbHlmaWxscy5janMuanM/OWE3OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICAgIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiBhc3NpZ24odGFyZ2V0KSB7XG4gICAgICAgIGZvcih2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspe1xuICAgICAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIGZvcih2YXIga2V5IGluIHNvdXJjZSlpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHNvdXJjZSwga2V5KSkgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGFyZ2V0O1xuICAgIH07XG4gICAgcmV0dXJuIF9leHRlbmRzLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbmZ1bmN0aW9uIF9vYmplY3Rfd2l0aG91dF9wcm9wZXJ0aWVzX2xvb3NlKHNvdXJjZSwgZXhjbHVkZWQpIHtcbiAgICBpZiAoc291cmNlID09IG51bGwpIHJldHVybiB7fTtcbiAgICB2YXIgdGFyZ2V0ID0ge307XG4gICAgdmFyIHNvdXJjZUtleXMgPSBPYmplY3Qua2V5cyhzb3VyY2UpO1xuICAgIHZhciBrZXksIGk7XG4gICAgZm9yKGkgPSAwOyBpIDwgc291cmNlS2V5cy5sZW5ndGg7IGkrKyl7XG4gICAgICAgIGtleSA9IHNvdXJjZUtleXNbaV07XG4gICAgICAgIGlmIChleGNsdWRlZC5pbmRleE9mKGtleSkgPj0gMCkgY29udGludWU7XG4gICAgICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gICAgfVxuICAgIHJldHVybiB0YXJnZXQ7XG59XG5cbmV4cG9ydHMuX2V4dGVuZHMgPSBfZXh0ZW5kcztcbmV4cG9ydHMuX29iamVjdF93aXRob3V0X3Byb3BlcnRpZXNfbG9vc2UgPSBfb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///6689\n\n}");

/***/ }),

/***/ 6817:
/***/ ((module) => {

eval("{\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/browser/index.ts\nvar browser_exports = {};\n__export(browser_exports, {\n  createLogger: () => createLogger2,\n  logger: () => logger\n});\nmodule.exports = __toCommonJS(browser_exports);\n\n// src/browser/color.ts\nvar supportsSubstitutions = void 0;\nvar supportColor = () => {\n  if (typeof supportsSubstitutions !== \"undefined\") {\n    return supportsSubstitutions;\n  }\n  try {\n    console.log(\"%c\", \"color:\");\n    supportsSubstitutions = true;\n  } catch (e) {\n    supportsSubstitutions = false;\n  }\n  return supportsSubstitutions;\n};\nvar ansiToCss = {\n  \"bold\": \"font-weight: bold;\",\n  \"red\": \"color: red;\",\n  \"green\": \"color: green;\",\n  \"orange\": \"color: orange;\",\n  \"dodgerblue\": \"color: dodgerblue;\",\n  \"magenta\": \"color: magenta;\",\n  \"gray\": \"color: gray;\"\n};\nvar formatter = (key) => supportColor() ? (input) => {\n  if (Array.isArray(input)) {\n    const [label, style] = input;\n    return [`%c${label.replace(\"%c\", \"\")}`, style ? `${ansiToCss[key]}${style}` : `${ansiToCss[key] || \"\"}`];\n  }\n  return [`%c${String(input).replace(\"%c\", \"\")}`, ansiToCss[key] || \"\"];\n} : (input) => [String(input)];\nvar bold = formatter(\"bold\");\nvar red = formatter(\"red\");\nvar green = formatter(\"green\");\nvar orange = formatter(\"orange\");\nvar dodgerblue = formatter(\"dodgerblue\");\nvar magenta = formatter(\"magenta\");\nvar gray = formatter(\"gray\");\n\n// src/browser/utils.ts\nfunction getLabel(type, logType, labels) {\n  let label = [\"\"];\n  if (\"label\" in logType) {\n    label = [labels[type] || logType.label || \"\"];\n    label = bold(logType.color ? logType.color(label) : label[0]);\n  }\n  label = label.filter(Boolean);\n  return label;\n}\nfunction finalLog(label, text, args, message) {\n  if (label.length) {\n    if (Array.isArray(message)) {\n      console.log(...label, ...message);\n    } else {\n      console.log(...label, text);\n    }\n  } else {\n    Array.isArray(message) ? console.log(...message) : console.log(text, ...args);\n  }\n}\n\n// src/constants.ts\nvar LOG_LEVEL = {\n  error: 0,\n  warn: 1,\n  info: 2,\n  log: 3,\n  verbose: 4\n};\n\n// src/utils.ts\nvar errorStackRegExp = /at\\s.*:\\d+:\\d+[\\s\\)]*$/;\nvar anonymousErrorStackRegExp = /at\\s.*\\(<anonymous>\\)$/;\nvar isErrorStackMessage = (message) => errorStackRegExp.test(message) || anonymousErrorStackRegExp.test(message);\n\n// src/createLogger.ts\nvar createLogger = (options = {}, { getLabel: getLabel2, handleError, finalLog: finalLog2, greet, LOG_TYPES: LOG_TYPES2 }) => {\n  let maxLevel = options.level || \"log\";\n  let customLabels = options.labels || {};\n  let log = (type, message, ...args) => {\n    if (LOG_LEVEL[LOG_TYPES2[type].level] > LOG_LEVEL[maxLevel]) {\n      return;\n    }\n    if (message === void 0 || message === null) {\n      return console.log();\n    }\n    let logType = LOG_TYPES2[type];\n    let text = \"\";\n    const label = getLabel2(type, logType, customLabels);\n    if (message instanceof Error) {\n      if (message.stack) {\n        let [name, ...rest] = message.stack.split(\"\\n\");\n        if (name.startsWith(\"Error: \")) {\n          name = name.slice(7);\n        }\n        text = `${name}\n${handleError(rest.join(\"\\n\"))}`;\n      } else {\n        text = message.message;\n      }\n    } else if (logType.level === \"error\" && typeof message === \"string\") {\n      let lines = message.split(\"\\n\");\n      text = lines.map((line) => isErrorStackMessage(line) ? handleError(line) : line).join(\"\\n\");\n    } else {\n      text = `${message}`;\n    }\n    finalLog2(label, text, args, message);\n  };\n  let logger2 = {\n    // greet\n    greet: (message) => log(\"log\", greet(message))\n  };\n  Object.keys(LOG_TYPES2).forEach((key) => {\n    logger2[key] = (...args) => log(key, ...args);\n  });\n  Object.defineProperty(logger2, \"level\", {\n    get: () => maxLevel,\n    set(val) {\n      maxLevel = val;\n    }\n  });\n  Object.defineProperty(logger2, \"labels\", {\n    get: () => customLabels,\n    set(val) {\n      customLabels = val;\n    }\n  });\n  logger2.override = (customLogger) => {\n    Object.assign(logger2, customLogger);\n  };\n  return logger2;\n};\n\n// src/browser/gradient.ts\nvar startColor = [189, 255, 243];\nvar endColor = [74, 194, 154];\nvar isWord = (char) => !/[\\s\\n]/.test(char);\nfunction gradient(message) {\n  if (!supportColor()) {\n    return [message];\n  }\n  const chars = [...message];\n  const words = chars.filter(isWord);\n  const steps = words.length - 1;\n  if (steps === 0) {\n    console.log(`%c${message}`, `color: rgb(${startColor.join(\",\")}); font-weight: bold;`);\n    return [message];\n  }\n  let output = \"\";\n  let styles = [];\n  chars.forEach((char) => {\n    if (isWord(char)) {\n      const progress = words.indexOf(char) / steps;\n      const r = Math.round(startColor[0] + (endColor[0] - startColor[0]) * progress);\n      const g = Math.round(startColor[1] + (endColor[1] - startColor[1]) * progress);\n      const b = Math.round(startColor[2] + (endColor[2] - startColor[2]) * progress);\n      output += `%c${char}`;\n      styles.push(`color: rgb(${r},${g},${b}); font-weight: bold;`);\n    } else {\n      output += char;\n    }\n  });\n  return [output, ...styles];\n}\n\n// src/browser/constants.ts\nvar LOG_TYPES = {\n  // Level error\n  error: {\n    label: \"error\",\n    level: \"error\",\n    color: red\n  },\n  // Level warn\n  warn: {\n    label: \"warn\",\n    level: \"warn\",\n    color: orange\n  },\n  // Level info\n  info: {\n    label: \"info\",\n    level: \"info\",\n    color: dodgerblue\n  },\n  start: {\n    label: \"start\",\n    level: \"info\",\n    color: dodgerblue\n  },\n  ready: {\n    label: \"ready\",\n    level: \"info\",\n    color: green\n  },\n  success: {\n    label: \"success\",\n    level: \"info\",\n    color: green\n  },\n  // Level log\n  log: {\n    level: \"log\"\n  },\n  // Level debug\n  debug: {\n    label: \"debug\",\n    level: \"verbose\",\n    color: magenta\n  }\n};\n\n// src/browser/createLogger.ts\nfunction createLogger2(options = {}) {\n  return createLogger(options, {\n    handleError: (msg) => msg,\n    getLabel,\n    gradient,\n    finalLog,\n    LOG_TYPES,\n    greet: (msg) => {\n      return gradient(msg);\n    }\n  });\n}\n\n// src/browser/index.ts\nvar logger = createLogger2();\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///6817\n\n}");

/***/ }),

/***/ 9399:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nexports._extends = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTM5OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQkFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL3RyYW5zY3JpcHQtYW5kLXN1bW1hcnkvLi4vLi4vbm9kZV9tb2R1bGVzL0Btb2R1bGUtZmVkZXJhdGlvbi93ZWJwYWNrLWJ1bmRsZXItcnVudGltZS9kaXN0L3BvbHlmaWxscy5janMuanM/ZDg2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICAgIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiBhc3NpZ24odGFyZ2V0KSB7XG4gICAgICAgIGZvcih2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspe1xuICAgICAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIGZvcih2YXIga2V5IGluIHNvdXJjZSlpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHNvdXJjZSwga2V5KSkgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGFyZ2V0O1xuICAgIH07XG4gICAgcmV0dXJuIF9leHRlbmRzLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydHMuX2V4dGVuZHMgPSBfZXh0ZW5kcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///9399\n\n}");

/***/ }),

/***/ 9468:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar isomorphicRslog = __webpack_require__(6817);\nvar polyfills = __webpack_require__(1784);\n\nconst FederationModuleManifest = 'federation-manifest.json';\nconst MANIFEST_EXT = '.json';\nconst BROWSER_LOG_KEY = 'FEDERATION_DEBUG';\nconst BROWSER_LOG_VALUE = '1';\nconst NameTransformSymbol = {\n    AT: '@',\n    HYPHEN: '-',\n    SLASH: '/'\n};\nconst NameTransformMap = {\n    [NameTransformSymbol.AT]: 'scope_',\n    [NameTransformSymbol.HYPHEN]: '_',\n    [NameTransformSymbol.SLASH]: '__'\n};\nconst EncodedNameTransformMap = {\n    [NameTransformMap[NameTransformSymbol.AT]]: NameTransformSymbol.AT,\n    [NameTransformMap[NameTransformSymbol.HYPHEN]]: NameTransformSymbol.HYPHEN,\n    [NameTransformMap[NameTransformSymbol.SLASH]]: NameTransformSymbol.SLASH\n};\nconst SEPARATOR = ':';\nconst ManifestFileName = 'mf-manifest.json';\nconst StatsFileName = 'mf-stats.json';\nconst MFModuleType = {\n    NPM: 'npm',\n    APP: 'app'\n};\nconst MODULE_DEVTOOL_IDENTIFIER = '__MF_DEVTOOLS_MODULE_INFO__';\nconst ENCODE_NAME_PREFIX = 'ENCODE_NAME_PREFIX';\nconst TEMP_DIR = '.federation';\nconst MFPrefetchCommon = {\n    identifier: 'MFDataPrefetch',\n    globalKey: '__PREFETCH__',\n    library: 'mf-data-prefetch',\n    exportsKey: '__PREFETCH_EXPORTS__',\n    fileName: 'bootstrap.js'\n};\n\nvar ContainerPlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar ContainerReferencePlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar ModuleFederationPlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar SharePlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nfunction isBrowserEnv() {\n    return typeof window !== 'undefined';\n}\nfunction isBrowserDebug() {\n    try {\n        if (isBrowserEnv() && window.localStorage) {\n            return localStorage.getItem(BROWSER_LOG_KEY) === BROWSER_LOG_VALUE;\n        }\n    } catch (error) {\n        return false;\n    }\n    return false;\n}\nfunction isDebugMode() {\n    if (typeof process !== 'undefined' && process.env && process.env['FEDERATION_DEBUG']) {\n        return Boolean(process.env['FEDERATION_DEBUG']);\n    }\n    if (typeof FEDERATION_DEBUG !== 'undefined' && Boolean(FEDERATION_DEBUG)) {\n        return true;\n    }\n    return isBrowserDebug();\n}\nconst getProcessEnv = function() {\n    return typeof process !== 'undefined' && process.env ? process.env : {};\n};\n\nconst PREFIX = '[ Module Federation ]';\nfunction setDebug(loggerInstance) {\n    if (isDebugMode()) {\n        loggerInstance.level = 'verbose';\n    }\n}\nfunction setPrefix(loggerInstance, prefix) {\n    loggerInstance.labels = {\n        warn: `${prefix} Warn`,\n        error: `${prefix} Error`,\n        success: `${prefix} Success`,\n        info: `${prefix} Info`,\n        ready: `${prefix} Ready`,\n        debug: `${prefix} Debug`\n    };\n}\nfunction createLogger(prefix) {\n    const loggerInstance = isomorphicRslog.createLogger({\n        labels: {\n            warn: `${PREFIX} Warn`,\n            error: `${PREFIX} Error`,\n            success: `${PREFIX} Success`,\n            info: `${PREFIX} Info`,\n            ready: `${PREFIX} Ready`,\n            debug: `${PREFIX} Debug`\n        }\n    });\n    setDebug(loggerInstance);\n    setPrefix(loggerInstance, prefix);\n    return loggerInstance;\n}\nconst logger = createLogger(PREFIX);\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// entry: name:version   version : 1.0.0 | ^1.2.3\n// entry: name:entry  entry:  https://localhost:9000/federation-manifest.json\nconst parseEntry = (str, devVerOrUrl, separator = SEPARATOR)=>{\n    const strSplit = str.split(separator);\n    const devVersionOrUrl = getProcessEnv()['NODE_ENV'] === 'development' && devVerOrUrl;\n    const defaultVersion = '*';\n    const isEntry = (s)=>s.startsWith('http') || s.includes(MANIFEST_EXT);\n    // Check if the string starts with a type\n    if (strSplit.length >= 2) {\n        let [name, ...versionOrEntryArr] = strSplit;\n        if (str.startsWith(separator)) {\n            versionOrEntryArr = [\n                devVersionOrUrl || strSplit.slice(-1)[0]\n            ];\n            name = strSplit.slice(0, -1).join(separator);\n        }\n        let versionOrEntry = devVersionOrUrl || versionOrEntryArr.join(separator);\n        if (isEntry(versionOrEntry)) {\n            return {\n                name,\n                entry: versionOrEntry\n            };\n        } else {\n            // Apply version rule\n            // devVersionOrUrl => inputVersion => defaultVersion\n            return {\n                name,\n                version: versionOrEntry || defaultVersion\n            };\n        }\n    } else if (strSplit.length === 1) {\n        const [name] = strSplit;\n        if (devVersionOrUrl && isEntry(devVersionOrUrl)) {\n            return {\n                name,\n                entry: devVersionOrUrl\n            };\n        }\n        return {\n            name,\n            version: devVersionOrUrl || defaultVersion\n        };\n    } else {\n        throw `Invalid entry value: ${str}`;\n    }\n};\nconst composeKeyWithSeparator = function(...args) {\n    if (!args.length) {\n        return '';\n    }\n    return args.reduce((sum, cur)=>{\n        if (!cur) {\n            return sum;\n        }\n        if (!sum) {\n            return cur;\n        }\n        return `${sum}${SEPARATOR}${cur}`;\n    }, '');\n};\nconst encodeName = function(name, prefix = '', withExt = false) {\n    try {\n        const ext = withExt ? '.js' : '';\n        return `${prefix}${name.replace(new RegExp(`${NameTransformSymbol.AT}`, 'g'), NameTransformMap[NameTransformSymbol.AT]).replace(new RegExp(`${NameTransformSymbol.HYPHEN}`, 'g'), NameTransformMap[NameTransformSymbol.HYPHEN]).replace(new RegExp(`${NameTransformSymbol.SLASH}`, 'g'), NameTransformMap[NameTransformSymbol.SLASH])}${ext}`;\n    } catch (err) {\n        throw err;\n    }\n};\nconst decodeName = function(name, prefix, withExt) {\n    try {\n        let decodedName = name;\n        if (prefix) {\n            if (!decodedName.startsWith(prefix)) {\n                return decodedName;\n            }\n            decodedName = decodedName.replace(new RegExp(prefix, 'g'), '');\n        }\n        decodedName = decodedName.replace(new RegExp(`${NameTransformMap[NameTransformSymbol.AT]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.AT]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.SLASH]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.SLASH]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.HYPHEN]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.HYPHEN]]);\n        if (withExt) {\n            decodedName = decodedName.replace('.js', '');\n        }\n        return decodedName;\n    } catch (err) {\n        throw err;\n    }\n};\nconst generateExposeFilename = (exposeName, withExt)=>{\n    if (!exposeName) {\n        return '';\n    }\n    let expose = exposeName;\n    if (expose === '.') {\n        expose = 'default_export';\n    }\n    if (expose.startsWith('./')) {\n        expose = expose.replace('./', '');\n    }\n    return encodeName(expose, '__federation_expose_', withExt);\n};\nconst generateShareFilename = (pkgName, withExt)=>{\n    if (!pkgName) {\n        return '';\n    }\n    return encodeName(pkgName, '__federation_shared_', withExt);\n};\nconst getResourceUrl = (module, sourceUrl)=>{\n    if ('getPublicPath' in module) {\n        let publicPath;\n        if (!module.getPublicPath.startsWith('function')) {\n            publicPath = new Function(module.getPublicPath)();\n        } else {\n            publicPath = new Function('return ' + module.getPublicPath)()();\n        }\n        return `${publicPath}${sourceUrl}`;\n    } else if ('publicPath' in module) {\n        return `${module.publicPath}${sourceUrl}`;\n    } else {\n        console.warn('Cannot get resource URL. If in debug mode, please ignore.', module, sourceUrl);\n        return '';\n    }\n};\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nconst assert = (condition, msg)=>{\n    if (!condition) {\n        error(msg);\n    }\n};\nconst error = (msg)=>{\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n};\nconst warn = (msg)=>{\n    console.warn(`${LOG_CATEGORY}: ${msg}`);\n};\nfunction safeToString(info) {\n    try {\n        return JSON.stringify(info, null, 2);\n    } catch (e) {\n        return '';\n    }\n}\n// RegExp for version string\nconst VERSION_PATTERN_REGEXP = /^([\\d^=v<>~]|[*xX]$)/;\nfunction isRequiredVersion(str) {\n    return VERSION_PATTERN_REGEXP.test(str);\n}\n\nconst simpleJoinRemoteEntry = (rPath, rName)=>{\n    if (!rPath) {\n        return rName;\n    }\n    const transformPath = (str)=>{\n        if (str === '.') {\n            return '';\n        }\n        if (str.startsWith('./')) {\n            return str.replace('./', '');\n        }\n        if (str.startsWith('/')) {\n            const strWithoutSlash = str.slice(1);\n            if (strWithoutSlash.endsWith('/')) {\n                return strWithoutSlash.slice(0, -1);\n            }\n            return strWithoutSlash;\n        }\n        return str;\n    };\n    const transformedPath = transformPath(rPath);\n    if (!transformedPath) {\n        return rName;\n    }\n    if (transformedPath.endsWith('/')) {\n        return `${transformedPath}${rName}`;\n    }\n    return `${transformedPath}/${rName}`;\n};\nfunction inferAutoPublicPath(url) {\n    return url.replace(/#.*$/, '').replace(/\\?.*$/, '').replace(/\\/[^\\/]+$/, '/');\n}\n// Priority: overrides > remotes\n// eslint-disable-next-line max-lines-per-function\nfunction generateSnapshotFromManifest(manifest, options = {}) {\n    var _manifest_metaData, _manifest_metaData1;\n    const { remotes = {}, overrides = {}, version } = options;\n    let remoteSnapshot;\n    const getPublicPath = ()=>{\n        if ('publicPath' in manifest.metaData) {\n            if (manifest.metaData.publicPath === 'auto' && version) {\n                // use same implementation as publicPath auto runtime module implements\n                return inferAutoPublicPath(version);\n            }\n            return manifest.metaData.publicPath;\n        } else {\n            return manifest.metaData.getPublicPath;\n        }\n    };\n    const overridesKeys = Object.keys(overrides);\n    let remotesInfo = {};\n    // If remotes are not provided, only the remotes in the manifest will be read\n    if (!Object.keys(remotes).length) {\n        var _manifest_remotes;\n        remotesInfo = ((_manifest_remotes = manifest.remotes) == null ? void 0 : _manifest_remotes.reduce((res, next)=>{\n            let matchedVersion;\n            const name = next.federationContainerName;\n            // overrides have higher priority\n            if (overridesKeys.includes(name)) {\n                matchedVersion = overrides[name];\n            } else {\n                if ('version' in next) {\n                    matchedVersion = next.version;\n                } else {\n                    matchedVersion = next.entry;\n                }\n            }\n            res[name] = {\n                matchedVersion\n            };\n            return res;\n        }, {})) || {};\n    }\n    // If remotes (deploy scenario) are specified, they need to be traversed again\n    Object.keys(remotes).forEach((key)=>remotesInfo[key] = {\n            // overrides will override dependencies\n            matchedVersion: overridesKeys.includes(key) ? overrides[key] : remotes[key]\n        });\n    const { remoteEntry: { path: remoteEntryPath, name: remoteEntryName, type: remoteEntryType }, types: remoteTypes, buildInfo: { buildVersion }, globalName, ssrRemoteEntry } = manifest.metaData;\n    const { exposes } = manifest;\n    let basicRemoteSnapshot = {\n        version: version ? version : '',\n        buildVersion,\n        globalName,\n        remoteEntry: simpleJoinRemoteEntry(remoteEntryPath, remoteEntryName),\n        remoteEntryType,\n        remoteTypes: simpleJoinRemoteEntry(remoteTypes.path, remoteTypes.name),\n        remoteTypesZip: remoteTypes.zip || '',\n        remoteTypesAPI: remoteTypes.api || '',\n        remotesInfo,\n        shared: manifest == null ? void 0 : manifest.shared.map((item)=>({\n                assets: item.assets,\n                sharedName: item.name,\n                version: item.version\n            })),\n        modules: exposes == null ? void 0 : exposes.map((expose)=>({\n                moduleName: expose.name,\n                modulePath: expose.path,\n                assets: expose.assets\n            }))\n    };\n    if ((_manifest_metaData = manifest.metaData) == null ? void 0 : _manifest_metaData.prefetchInterface) {\n        const prefetchInterface = manifest.metaData.prefetchInterface;\n        basicRemoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            prefetchInterface\n        });\n    }\n    if ((_manifest_metaData1 = manifest.metaData) == null ? void 0 : _manifest_metaData1.prefetchEntry) {\n        const { path, name, type } = manifest.metaData.prefetchEntry;\n        basicRemoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            prefetchEntry: simpleJoinRemoteEntry(path, name),\n            prefetchEntryType: type\n        });\n    }\n    if ('publicPath' in manifest.metaData) {\n        remoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            publicPath: getPublicPath()\n        });\n    } else {\n        remoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            getPublicPath: getPublicPath()\n        });\n    }\n    if (ssrRemoteEntry) {\n        const fullSSRRemoteEntry = simpleJoinRemoteEntry(ssrRemoteEntry.path, ssrRemoteEntry.name);\n        remoteSnapshot.ssrRemoteEntry = fullSSRRemoteEntry;\n        remoteSnapshot.ssrRemoteEntryType = ssrRemoteEntry.type || 'commonjs-module';\n    }\n    return remoteSnapshot;\n}\nfunction isManifestProvider(moduleInfo) {\n    if ('remoteEntry' in moduleInfo && moduleInfo.remoteEntry.includes(MANIFEST_EXT)) {\n        return true;\n    } else {\n        return false;\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function safeWrapper(callback, disableWarn) {\n    try {\n        const res = await callback();\n        return res;\n    } catch (e) {\n        !disableWarn && warn(e);\n        return;\n    }\n}\nfunction isStaticResourcesEqual(url1, url2) {\n    const REG_EXP = /^(https?:)?\\/\\//i;\n    // Transform url1 and url2 into relative paths\n    const relativeUrl1 = url1.replace(REG_EXP, '').replace(/\\/$/, '');\n    const relativeUrl2 = url2.replace(REG_EXP, '').replace(/\\/$/, '');\n    // Check if the relative paths are identical\n    return relativeUrl1 === relativeUrl2;\n}\nfunction createScript(info) {\n    // Retrieve the existing script element by its src attribute\n    let script = null;\n    let needAttach = true;\n    let timeout = 20000;\n    let timeoutId;\n    const scripts = document.getElementsByTagName('script');\n    for(let i = 0; i < scripts.length; i++){\n        const s = scripts[i];\n        const scriptSrc = s.getAttribute('src');\n        if (scriptSrc && isStaticResourcesEqual(scriptSrc, info.url)) {\n            script = s;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!script) {\n        const attrs = info.attrs;\n        script = document.createElement('script');\n        script.type = (attrs == null ? void 0 : attrs['type']) === 'module' ? 'module' : 'text/javascript';\n        let createScriptRes = undefined;\n        if (info.createScriptHook) {\n            createScriptRes = info.createScriptHook(info.url, info.attrs);\n            if (createScriptRes instanceof HTMLScriptElement) {\n                script = createScriptRes;\n            } else if (typeof createScriptRes === 'object') {\n                if ('script' in createScriptRes && createScriptRes.script) {\n                    script = createScriptRes.script;\n                }\n                if ('timeout' in createScriptRes && createScriptRes.timeout) {\n                    timeout = createScriptRes.timeout;\n                }\n            }\n        }\n        if (!script.src) {\n            script.src = info.url;\n        }\n        if (attrs && !createScriptRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (script) {\n                    if (name === 'async' || name === 'defer') {\n                        script[name] = attrs[name];\n                    // Attributes that do not exist are considered overridden\n                    } else if (!script.getAttribute(name)) {\n                        script.setAttribute(name, attrs[name]);\n                    }\n                }\n            });\n        }\n    }\n    const onScriptComplete = async (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        var _info_cb;\n        clearTimeout(timeoutId);\n        // Prevent memory leaks in IE.\n        if (script) {\n            script.onerror = null;\n            script.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteScript = true } = info;\n                if (needDeleteScript) {\n                    (script == null ? void 0 : script.parentNode) && script.parentNode.removeChild(script);\n                }\n            });\n            if (prev && typeof prev === 'function') {\n                var _info_cb1;\n                const result = prev(event);\n                if (result instanceof Promise) {\n                    var _info_cb2;\n                    const res = await result;\n                    info == null ? void 0 : (_info_cb2 = info.cb) == null ? void 0 : _info_cb2.call(info);\n                    return res;\n                }\n                info == null ? void 0 : (_info_cb1 = info.cb) == null ? void 0 : _info_cb1.call(info);\n                return result;\n            }\n        }\n        info == null ? void 0 : (_info_cb = info.cb) == null ? void 0 : _info_cb.call(info);\n    };\n    script.onerror = onScriptComplete.bind(null, script.onerror);\n    script.onload = onScriptComplete.bind(null, script.onload);\n    timeoutId = setTimeout(()=>{\n        onScriptComplete(null, new Error(`Remote script \"${info.url}\" time-outed.`));\n    }, timeout);\n    return {\n        script,\n        needAttach\n    };\n}\nfunction createLink(info) {\n    // <link rel=\"preload\" href=\"script.js\" as=\"script\">\n    // Retrieve the existing script element by its src attribute\n    let link = null;\n    let needAttach = true;\n    const links = document.getElementsByTagName('link');\n    for(let i = 0; i < links.length; i++){\n        const l = links[i];\n        const linkHref = l.getAttribute('href');\n        const linkRef = l.getAttribute('ref');\n        if (linkHref && isStaticResourcesEqual(linkHref, info.url) && linkRef === info.attrs['ref']) {\n            link = l;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!link) {\n        link = document.createElement('link');\n        link.setAttribute('href', info.url);\n        let createLinkRes = undefined;\n        const attrs = info.attrs;\n        if (info.createLinkHook) {\n            createLinkRes = info.createLinkHook(info.url, attrs);\n            if (createLinkRes instanceof HTMLLinkElement) {\n                link = createLinkRes;\n            }\n        }\n        if (attrs && !createLinkRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (link && !link.getAttribute(name)) {\n                    link.setAttribute(name, attrs[name]);\n                }\n            });\n        }\n    }\n    const onLinkComplete = (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        // Prevent memory leaks in IE.\n        if (link) {\n            link.onerror = null;\n            link.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteLink = true } = info;\n                if (needDeleteLink) {\n                    (link == null ? void 0 : link.parentNode) && link.parentNode.removeChild(link);\n                }\n            });\n            if (prev) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const res = prev(event);\n                info.cb();\n                return res;\n            }\n        }\n        info.cb();\n    };\n    link.onerror = onLinkComplete.bind(null, link.onerror);\n    link.onload = onLinkComplete.bind(null, link.onload);\n    return {\n        link,\n        needAttach\n    };\n}\nfunction loadScript(url, info) {\n    const { attrs = {}, createScriptHook } = info;\n    return new Promise((resolve, _reject)=>{\n        const { script, needAttach } = createScript({\n            url,\n            cb: resolve,\n            attrs: polyfills._extends({\n                fetchpriority: 'high'\n            }, attrs),\n            createScriptHook,\n            needDeleteScript: true\n        });\n        needAttach && document.head.appendChild(script);\n    });\n}\n\nfunction importNodeModule(name) {\n    if (!name) {\n        throw new Error('import specifier is required');\n    }\n    const importModule = new Function('name', `return import(name)`);\n    return importModule(name).then((res)=>res).catch((error)=>{\n        console.error(`Error importing module ${name}:`, error);\n        throw error;\n    });\n}\nconst loadNodeFetch = async ()=>{\n    const fetchModule = await importNodeModule('node-fetch');\n    return fetchModule.default || fetchModule;\n};\nconst lazyLoaderHookFetch = async (input, init)=>{\n    // @ts-ignore\n    const loaderHooks = __webpack_require__.federation.instance.loaderHook;\n    const hook = (url, init)=>{\n        return loaderHooks.lifecycle.fetch.emit(url, init);\n    };\n    const res = await hook(input, init || {});\n    if (!res || !(res instanceof Response)) {\n        const fetchFunction = typeof fetch === 'undefined' ? await loadNodeFetch() : fetch;\n        return fetchFunction(input, init || {});\n    }\n    return res;\n};\nfunction createScriptNode(url, cb, attrs, createScriptHook) {\n    if (createScriptHook) {\n        const hookResult = createScriptHook(url);\n        if (hookResult && typeof hookResult === 'object' && 'url' in hookResult) {\n            url = hookResult.url;\n        }\n    }\n    let urlObj;\n    try {\n        urlObj = new URL(url);\n    } catch (e) {\n        console.error('Error constructing URL:', e);\n        cb(new Error(`Invalid URL: ${e}`));\n        return;\n    }\n    const getFetch = async ()=>{\n        //@ts-ignore\n        if (true) {\n            try {\n                //@ts-ignore\n                const loaderHooks = __webpack_require__.federation.instance.loaderHook;\n                if (loaderHooks.lifecycle.fetch) {\n                    return lazyLoaderHookFetch;\n                }\n            } catch (e) {\n                console.warn('federation.instance.loaderHook.lifecycle.fetch failed:', e);\n            }\n        }\n        return typeof fetch === 'undefined' ? loadNodeFetch() : fetch;\n    };\n    const handleScriptFetch = async (f, urlObj)=>{\n        try {\n            var //@ts-ignore\n            _vm_constants;\n            const res = await f(urlObj.href);\n            const data = await res.text();\n            const [path, vm] = await Promise.all([\n                importNodeModule('path'),\n                importNodeModule('vm')\n            ]);\n            const scriptContext = {\n                exports: {},\n                module: {\n                    exports: {}\n                }\n            };\n            const urlDirname = urlObj.pathname.split('/').slice(0, -1).join('/');\n            const filename = path.basename(urlObj.pathname);\n            var _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER;\n            const script = new vm.Script(`(function(exports, module, require, __dirname, __filename) {${data}\\n})`, {\n                filename,\n                importModuleDynamically: (_vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER = (_vm_constants = vm.constants) == null ? void 0 : _vm_constants.USE_MAIN_CONTEXT_DEFAULT_LOADER) != null ? _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER : importNodeModule\n            });\n            script.runInThisContext()(scriptContext.exports, scriptContext.module, eval('require'), urlDirname, filename);\n            const exportedInterface = scriptContext.module.exports || scriptContext.exports;\n            if (attrs && exportedInterface && attrs['globalName']) {\n                const container = exportedInterface[attrs['globalName']] || exportedInterface;\n                cb(undefined, container);\n                return;\n            }\n            cb(undefined, exportedInterface);\n        } catch (e) {\n            cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n        }\n    };\n    getFetch().then(async (f)=>{\n        if ((attrs == null ? void 0 : attrs['type']) === 'esm' || (attrs == null ? void 0 : attrs['type']) === 'module') {\n            return loadModule(urlObj.href, {\n                fetch: f,\n                vm: await importNodeModule('vm')\n            }).then(async (module)=>{\n                await module.evaluate();\n                cb(undefined, module.namespace);\n            }).catch((e)=>{\n                cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n            });\n        }\n        handleScriptFetch(f, urlObj);\n    }).catch((err)=>{\n        cb(err);\n    });\n}\nfunction loadScriptNode(url, info) {\n    return new Promise((resolve, reject)=>{\n        createScriptNode(url, (error, scriptContext)=>{\n            if (error) {\n                reject(error);\n            } else {\n                var _info_attrs, _info_attrs1;\n                const remoteEntryKey = (info == null ? void 0 : (_info_attrs = info.attrs) == null ? void 0 : _info_attrs['globalName']) || `__FEDERATION_${info == null ? void 0 : (_info_attrs1 = info.attrs) == null ? void 0 : _info_attrs1['name']}:custom__`;\n                const entryExports = globalThis[remoteEntryKey] = scriptContext;\n                resolve(entryExports);\n            }\n        }, info.attrs, info.createScriptHook);\n    });\n}\nasync function loadModule(url, options) {\n    const { fetch: fetch1, vm } = options;\n    const response = await fetch1(url);\n    const code = await response.text();\n    const module = new vm.SourceTextModule(code, {\n        // @ts-ignore\n        importModuleDynamically: async (specifier, script)=>{\n            const resolvedUrl = new URL(specifier, url).href;\n            return loadModule(resolvedUrl, options);\n        }\n    });\n    await module.link(async (specifier)=>{\n        const resolvedUrl = new URL(specifier, url).href;\n        const module = await loadModule(resolvedUrl, options);\n        return module;\n    });\n    return module;\n}\n\nfunction normalizeOptions(enableDefault, defaultOptions, key) {\n    return function(options) {\n        if (options === false) {\n            return false;\n        }\n        if (typeof options === 'undefined') {\n            if (enableDefault) {\n                return defaultOptions;\n            } else {\n                return false;\n            }\n        }\n        if (options === true) {\n            return defaultOptions;\n        }\n        if (options && typeof options === 'object') {\n            return polyfills._extends({}, defaultOptions, options);\n        }\n        throw new Error(`Unexpected type for \\`${key}\\`, expect boolean/undefined/object, got: ${typeof options}`);\n    };\n}\n\nexports.BROWSER_LOG_KEY = BROWSER_LOG_KEY;\nexports.BROWSER_LOG_VALUE = BROWSER_LOG_VALUE;\nexports.ENCODE_NAME_PREFIX = ENCODE_NAME_PREFIX;\nexports.EncodedNameTransformMap = EncodedNameTransformMap;\nexports.FederationModuleManifest = FederationModuleManifest;\nexports.MANIFEST_EXT = MANIFEST_EXT;\nexports.MFModuleType = MFModuleType;\nexports.MFPrefetchCommon = MFPrefetchCommon;\nexports.MODULE_DEVTOOL_IDENTIFIER = MODULE_DEVTOOL_IDENTIFIER;\nexports.ManifestFileName = ManifestFileName;\nexports.NameTransformMap = NameTransformMap;\nexports.NameTransformSymbol = NameTransformSymbol;\nexports.SEPARATOR = SEPARATOR;\nexports.StatsFileName = StatsFileName;\nexports.TEMP_DIR = TEMP_DIR;\nexports.assert = assert;\nexports.composeKeyWithSeparator = composeKeyWithSeparator;\nexports.containerPlugin = ContainerPlugin;\nexports.containerReferencePlugin = ContainerReferencePlugin;\nexports.createLink = createLink;\nexports.createLogger = createLogger;\nexports.createScript = createScript;\nexports.createScriptNode = createScriptNode;\nexports.decodeName = decodeName;\nexports.encodeName = encodeName;\nexports.error = error;\nexports.generateExposeFilename = generateExposeFilename;\nexports.generateShareFilename = generateShareFilename;\nexports.generateSnapshotFromManifest = generateSnapshotFromManifest;\nexports.getProcessEnv = getProcessEnv;\nexports.getResourceUrl = getResourceUrl;\nexports.inferAutoPublicPath = inferAutoPublicPath;\nexports.isBrowserEnv = isBrowserEnv;\nexports.isDebugMode = isDebugMode;\nexports.isManifestProvider = isManifestProvider;\nexports.isRequiredVersion = isRequiredVersion;\nexports.isStaticResourcesEqual = isStaticResourcesEqual;\nexports.loadScript = loadScript;\nexports.loadScriptNode = loadScriptNode;\nexports.logger = logger;\nexports.moduleFederationPlugin = ModuleFederationPlugin;\nexports.normalizeOptions = normalizeOptions;\nexports.parseEntry = parseEntry;\nexports.safeToString = safeToString;\nexports.safeWrapper = safeWrapper;\nexports.sharePlugin = SharePlugin;\nexports.simpleJoinRemoteEntry = simpleJoinRemoteEntry;\nexports.warn = warn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///9468\n\n}");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var execOptions = { id: moduleId, module: module, factory: __webpack_modules__[moduleId], require: __webpack_require__ };
/******/ 		__webpack_require__.i.forEach(function(handler) { handler(execOptions); });
/******/ 		module = execOptions.module;
/******/ 		execOptions.factory.call(module.exports, module, module.exports, execOptions.require);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = __webpack_module_cache__;
/******/ 	
/******/ 	// expose the module execution interceptor
/******/ 	__webpack_require__.i = [];
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/federation runtime */
/******/ 	(() => {
/******/ 		if(!__webpack_require__.federation){
/******/ 			__webpack_require__.federation = {
/******/ 				initOptions: {"name":"transcriptAndSummary","remotes":[],"shareStrategy":"version-first"},
/******/ 				chunkMatcher: function(chunkId) {return true},
/******/ 				rootOutputDir: "",
/******/ 				initialConsumes: undefined,
/******/ 				bundlerRuntimeOptions: {}
/******/ 			};
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/load script */
/******/ 	(() => {
/******/ 		var inProgress = {};
/******/ 		var dataWebpackPrefix = "@crm/transcript-and-summary:";
/******/ 		// loadScript function to load a script via script tag
/******/ 		__webpack_require__.l = (url, done, key, chunkId) => {
/******/ 			if(inProgress[url]) { inProgress[url].push(done); return; }
/******/ 			var script, needAttach;
/******/ 			if(key !== undefined) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				for(var i = 0; i < scripts.length; i++) {
/******/ 					var s = scripts[i];
/******/ 					if(s.getAttribute("src") == url || s.getAttribute("data-webpack") == dataWebpackPrefix + key) { script = s; break; }
/******/ 				}
/******/ 			}
/******/ 			if(!script) {
/******/ 				needAttach = true;
/******/ 				script = document.createElement('script');
/******/ 		
/******/ 				script.charset = 'utf-8';
/******/ 				script.timeout = 120;
/******/ 				if (__webpack_require__.nc) {
/******/ 					script.setAttribute("nonce", __webpack_require__.nc);
/******/ 				}
/******/ 				script.setAttribute("data-webpack", dataWebpackPrefix + key);
/******/ 		
/******/ 				script.src = url;
/******/ 			}
/******/ 			inProgress[url] = [done];
/******/ 			var onScriptComplete = (prev, event) => {
/******/ 				// avoid mem leaks in IE.
/******/ 				script.onerror = script.onload = null;
/******/ 				clearTimeout(timeout);
/******/ 				var doneFns = inProgress[url];
/******/ 				delete inProgress[url];
/******/ 				script.parentNode && script.parentNode.removeChild(script);
/******/ 				doneFns && doneFns.forEach((fn) => (fn(event)));
/******/ 				if(prev) return prev(event);
/******/ 			}
/******/ 			var timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);
/******/ 			script.onerror = onScriptComplete.bind(null, script.onerror);
/******/ 			script.onload = onScriptComplete.bind(null, script.onload);
/******/ 			needAttach && document.head.appendChild(script);
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.nmd = (module) => {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/sharing */
/******/ 	(() => {
/******/ 		__webpack_require__.S = {};
/******/ 		var initPromises = {};
/******/ 		var initTokens = {};
/******/ 		__webpack_require__.I = (name, initScope) => {
/******/ 			if(!initScope) initScope = [];
/******/ 			// handling circular init calls
/******/ 			var initToken = initTokens[name];
/******/ 			if(!initToken) initToken = initTokens[name] = {};
/******/ 			if(initScope.indexOf(initToken) >= 0) return;
/******/ 			initScope.push(initToken);
/******/ 			// only runs once
/******/ 			if(initPromises[name]) return initPromises[name];
/******/ 			// creates a new share scope if needed
/******/ 			if(!__webpack_require__.o(__webpack_require__.S, name)) __webpack_require__.S[name] = {};
/******/ 			// runs all init snippets from all modules reachable
/******/ 			var scope = __webpack_require__.S[name];
/******/ 			var warn = (msg) => {
/******/ 				if (typeof console !== "undefined" && console.warn) console.warn(msg);
/******/ 			};
/******/ 			var uniqueName = "@crm/transcript-and-summary";
/******/ 			var register = (name, version, factory, eager) => {
/******/ 				var versions = scope[name] = scope[name] || {};
/******/ 				var activeVersion = versions[version];
/******/ 				if(!activeVersion || (!activeVersion.loaded && (!eager != !activeVersion.eager ? eager : uniqueName > activeVersion.from))) versions[version] = { get: factory, from: uniqueName, eager: !!eager };
/******/ 			};
/******/ 			var initExternal = (id) => {
/******/ 				var handleError = (err) => (warn("Initialization of sharing external failed: " + err));
/******/ 				try {
/******/ 					var module = __webpack_require__(id);
/******/ 					if(!module) return;
/******/ 					var initFn = (module) => (module && module.init && module.init(__webpack_require__.S[name], initScope))
/******/ 					if(module.then) return promises.push(module.then(initFn, handleError));
/******/ 					var initResult = initFn(module);
/******/ 					if(initResult && initResult.then) return promises.push(initResult['catch'](handleError));
/******/ 				} catch(err) { handleError(err); }
/******/ 			}
/******/ 			var promises = [];
/******/ 			switch(name) {
/******/ 				case "default": {
/******/ 					register("react-dom/client", "18.3.1", () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))));
/******/ 					register("react-dom", "18.3.1", () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))));
/******/ 					register("react/jsx-runtime", "18.3.1", () => (__webpack_require__.e(85).then(() => (() => (__webpack_require__(1085))))));
/******/ 					register("react", "18.3.1", () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))));
/******/ 				}
/******/ 				break;
/******/ 			}
/******/ 			if(!promises.length) return initPromises[name] = 1;
/******/ 			return initPromises[name] = Promise.all(promises).then(() => (initPromises[name] = 1));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/sharing */
/******/ 	(() => {
/******/ 		__webpack_require__.federation.initOptions.shared = {	"react-dom/client": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react-dom": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react/jsx-runtime": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(85).then(() => (() => (__webpack_require__(1085))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],}
/******/ 		__webpack_require__.S = {};
/******/ 		var initPromises = {};
/******/ 		var initTokens = {};
/******/ 		__webpack_require__.I = (name, initScope) => {
/******/ 			return __webpack_require__.federation.bundlerRuntime.I({	shareScopeName: name,
/******/ 				webpackRequire: __webpack_require__,
/******/ 				initPromises: initPromises,
/******/ 				initTokens: initTokens,
/******/ 				initScope: initScope,
/******/ 			})
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		var scriptUrl;
/******/ 		if (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + "";
/******/ 		var document = __webpack_require__.g.document;
/******/ 		if (!scriptUrl && document) {
/******/ 			if (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')
/******/ 				scriptUrl = document.currentScript.src;
/******/ 			if (!scriptUrl) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				if(scripts.length) {
/******/ 					var i = scripts.length - 1;
/******/ 					while (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;
/******/ 				}
/******/ 			}
/******/ 		}
/******/ 		// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration
/******/ 		// or pass an empty string ("") and set the __webpack_public_path__ variable from your code to use your own logic.
/******/ 		if (!scriptUrl) throw new Error("Automatic publicPath is not supported in this browser");
/******/ 		scriptUrl = scriptUrl.replace(/^blob:/, "").replace(/#.*$/, "").replace(/\?.*$/, "").replace(/\/[^\/]+$/, "/");
/******/ 		__webpack_require__.p = scriptUrl;
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/consumes */
/******/ 	(() => {
/******/ 		var installedModules = {};
/******/ 		var moduleToHandlerMapping = {
/******/ 			6300: {
/******/ 				getter: () => (__webpack_require__.e(466).then(() => (() => (__webpack_require__(1085))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react/jsx-runtime",
/******/ 			},
/******/ 			3355: {
/******/ 				getter: () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react",
/******/ 			},
/******/ 			6438: {
/******/ 				getter: () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react-dom/client",
/******/ 			},
/******/ 			7233: {
/******/ 				getter: () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react-dom",
/******/ 			}
/******/ 		};
/******/ 		// no consumes in initial chunks
/******/ 		var chunkMapping = {
/******/ 			"85": [
/******/ 				3355
/******/ 			],
/******/ 			"144": [
/******/ 				3355
/******/ 			],
/******/ 			"492": [
/******/ 				6300,
/******/ 				3355,
/******/ 				6438
/******/ 			],
/******/ 			"873": [
/******/ 				7233
/******/ 			]
/******/ 		};
/******/ 		__webpack_require__.f.consumes = (chunkId, promises) => {
/******/ 			__webpack_require__.federation.bundlerRuntime.consumes({
/******/ 			chunkMapping: chunkMapping,
/******/ 			installedModules: installedModules,
/******/ 			chunkId: chunkId,
/******/ 			moduleToHandlerMapping: moduleToHandlerMapping,
/******/ 			promises: promises,
/******/ 			webpackRequire:__webpack_require__
/******/ 			});
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		__webpack_require__.b = document.baseURI || self.location.href;
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			792: 0
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.f.j = (chunkId, promises) => {
/******/ 				// JSONP chunk loading for javascript
/******/ 				var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
/******/ 				if(installedChunkData !== 0) { // 0 means "already installed".
/******/ 		
/******/ 					// a Promise means "currently loading".
/******/ 					if(installedChunkData) {
/******/ 						promises.push(installedChunkData[2]);
/******/ 					} else {
/******/ 						if(true) { // all chunks have JS
/******/ 							// setup Promise in chunk cache
/******/ 							var promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));
/******/ 							promises.push(installedChunkData[2] = promise);
/******/ 		
/******/ 							// start chunk loading
/******/ 							var url = __webpack_require__.p + __webpack_require__.u(chunkId);
/******/ 							// create error before stack unwound to get useful stacktrace later
/******/ 							var error = new Error();
/******/ 							var loadingEnded = (event) => {
/******/ 								if(__webpack_require__.o(installedChunks, chunkId)) {
/******/ 									installedChunkData = installedChunks[chunkId];
/******/ 									if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
/******/ 									if(installedChunkData) {
/******/ 										var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 										var realSrc = event && event.target && event.target.src;
/******/ 										error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
/******/ 										error.name = 'ChunkLoadError';
/******/ 										error.type = errorType;
/******/ 										error.request = realSrc;
/******/ 										installedChunkData[1](error);
/******/ 									}
/******/ 								}
/******/ 							};
/******/ 							__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 		};
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		// no on chunks loaded
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 		
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunk_crm_transcript_and_summary"] = self["webpackChunk_crm_transcript_and_summary"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// module cache are used so entry inlining is disabled
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	__webpack_require__(391);
/******/ 	var __webpack_exports__ = __webpack_require__(4639);
/******/ 	
/******/ })()
;