{"id": "ifPartyMaster", "name": "ifPartyMaster", "metaData": {"name": "ifPartyMaster", "type": "app", "buildInfo": {"buildVersion": "1.0.0", "buildName": "@crm/if-party-master"}, "remoteEntry": {"name": "remoteEntry.js", "path": "", "type": "global"}, "types": {"path": "", "name": "", "zip": "", "api": ""}, "globalName": "ifPartyMaster", "pluginVersion": "0.6.16", "publicPath": "auto", "prefetchInterface": false}, "shared": [{"id": "ifPartyMaster:react-dom/client", "name": "react-dom/client", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["144.js", "41.js"], "sync": ["873.js"]}, "css": {"async": [], "sync": []}}}, {"id": "ifPartyMaster:react-dom", "name": "react-dom", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["41.js"], "sync": ["144.js"]}, "css": {"async": [], "sync": []}}}, {"id": "ifPartyMaster:react/jsx-runtime", "name": "react/jsx-runtime", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": ["41.js"], "sync": ["85.js", "466.js"]}, "css": {"async": [], "sync": []}}}, {"id": "ifPartyMaster:react", "name": "react", "version": "18.3.1", "singleton": true, "requiredVersion": "^18.3.1", "assets": {"js": {"async": [], "sync": ["41.js"]}, "css": {"async": [], "sync": []}}}], "remotes": [], "exposes": [{"id": "ifPartyMaster:App", "name": "App", "assets": {"js": {"sync": ["__federation_expose_App.js"], "async": ["466.js", "41.js", "567.js"]}, "css": {"sync": [], "async": []}}, "path": "./App"}]}