/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 1784:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nexports._extends = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTc4NC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQkFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL2lmLXBhcnR5LW1hc3Rlci8uLi8uLi9ub2RlX21vZHVsZXMvQG1vZHVsZS1mZWRlcmF0aW9uL3Nkay9kaXN0L3BvbHlmaWxscy5janMuanM/MjY1ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICAgIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiBhc3NpZ24odGFyZ2V0KSB7XG4gICAgICAgIGZvcih2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspe1xuICAgICAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIGZvcih2YXIga2V5IGluIHNvdXJjZSlpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHNvdXJjZSwga2V5KSkgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGFyZ2V0O1xuICAgIH07XG4gICAgcmV0dXJuIF9leHRlbmRzLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydHMuX2V4dGVuZHMgPSBfZXh0ZW5kcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///1784\n\n}");

/***/ }),

/***/ 2214:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar polyfills = __webpack_require__(6689);\nvar sdk = __webpack_require__(9468);\n\nfunction getBuilderId() {\n    //@ts-ignore\n    return  true ? \"ifPartyMaster:1.0.0\" : 0;\n}\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// FIXME: pre-bundle ?\nconst logger = sdk.createLogger(LOG_CATEGORY);\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction assert(condition, msg) {\n    if (!condition) {\n        error(msg);\n    }\n}\nfunction error(msg) {\n    if (msg instanceof Error) {\n        msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        throw msg;\n    }\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n}\nfunction warn(msg) {\n    if (msg instanceof Error) {\n        msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        logger.warn(msg);\n    } else {\n        logger.warn(msg);\n    }\n}\n\nfunction addUniqueItem(arr, item) {\n    if (arr.findIndex((name)=>name === item) === -1) {\n        arr.push(item);\n    }\n    return arr;\n}\nfunction getFMId(remoteInfo) {\n    if ('version' in remoteInfo && remoteInfo.version) {\n        return `${remoteInfo.name}:${remoteInfo.version}`;\n    } else if ('entry' in remoteInfo && remoteInfo.entry) {\n        return `${remoteInfo.name}:${remoteInfo.entry}`;\n    } else {\n        return `${remoteInfo.name}`;\n    }\n}\nfunction isRemoteInfoWithEntry(remote) {\n    return typeof remote.entry !== 'undefined';\n}\nfunction isPureRemoteEntry(remote) {\n    return !remote.entry.includes('.json') && remote.entry.includes('.js');\n}\nfunction isObject(val) {\n    return val && typeof val === 'object';\n}\nconst objectToString = Object.prototype.toString;\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isPlainObject(val) {\n    return objectToString.call(val) === '[object Object]';\n}\nfunction arrayOptions(options) {\n    return Array.isArray(options) ? options : [\n        options\n    ];\n}\nfunction getRemoteEntryInfoFromSnapshot(snapshot) {\n    const defaultRemoteEntryInfo = {\n        url: '',\n        type: 'global',\n        globalName: ''\n    };\n    if (sdk.isBrowserEnv()) {\n        return 'remoteEntry' in snapshot ? {\n            url: snapshot.remoteEntry,\n            type: snapshot.remoteEntryType,\n            globalName: snapshot.globalName\n        } : defaultRemoteEntryInfo;\n    }\n    if ('ssrRemoteEntry' in snapshot) {\n        return {\n            url: snapshot.ssrRemoteEntry || defaultRemoteEntryInfo.url,\n            type: snapshot.ssrRemoteEntryType || defaultRemoteEntryInfo.type,\n            globalName: snapshot.globalName\n        };\n    }\n    return defaultRemoteEntryInfo;\n}\n\nconst nativeGlobal = (()=>{\n    try {\n        return new Function('return this')();\n    } catch (e) {\n        return globalThis;\n    }\n})();\nconst Global = nativeGlobal;\nfunction definePropertyGlobalVal(target, key, val) {\n    Object.defineProperty(target, key, {\n        value: val,\n        configurable: false,\n        writable: true\n    });\n}\nfunction includeOwnProperty(target, key) {\n    return Object.hasOwnProperty.call(target, key);\n}\n// This section is to prevent encapsulation by certain microfrontend frameworks. Due to reuse policies, sandbox escapes.\n// The sandbox in the microfrontend does not replicate the value of 'configurable'.\n// If there is no loading content on the global object, this section defines the loading object.\nif (!includeOwnProperty(globalThis, '__GLOBAL_LOADING_REMOTE_ENTRY__')) {\n    definePropertyGlobalVal(globalThis, '__GLOBAL_LOADING_REMOTE_ENTRY__', {});\n}\nconst globalLoading = globalThis.__GLOBAL_LOADING_REMOTE_ENTRY__;\nfunction setGlobalDefaultVal(target) {\n    var _target___FEDERATION__, _target___FEDERATION__1, _target___FEDERATION__2, _target___FEDERATION__3, _target___FEDERATION__4, _target___FEDERATION__5;\n    if (includeOwnProperty(target, '__VMOK__') && !includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', target.__VMOK__);\n    }\n    if (!includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', {\n            __GLOBAL_PLUGIN__: [],\n            __INSTANCES__: [],\n            moduleInfo: {},\n            __SHARE__: {},\n            __MANIFEST_LOADING__: {},\n            __PRELOADED_MAP__: new Map()\n        });\n        definePropertyGlobalVal(target, '__VMOK__', target.__FEDERATION__);\n    }\n    var ___GLOBAL_PLUGIN__;\n    (___GLOBAL_PLUGIN__ = (_target___FEDERATION__ = target.__FEDERATION__).__GLOBAL_PLUGIN__) != null ? ___GLOBAL_PLUGIN__ : _target___FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    var ___INSTANCES__;\n    (___INSTANCES__ = (_target___FEDERATION__1 = target.__FEDERATION__).__INSTANCES__) != null ? ___INSTANCES__ : _target___FEDERATION__1.__INSTANCES__ = [];\n    var _moduleInfo;\n    (_moduleInfo = (_target___FEDERATION__2 = target.__FEDERATION__).moduleInfo) != null ? _moduleInfo : _target___FEDERATION__2.moduleInfo = {};\n    var ___SHARE__;\n    (___SHARE__ = (_target___FEDERATION__3 = target.__FEDERATION__).__SHARE__) != null ? ___SHARE__ : _target___FEDERATION__3.__SHARE__ = {};\n    var ___MANIFEST_LOADING__;\n    (___MANIFEST_LOADING__ = (_target___FEDERATION__4 = target.__FEDERATION__).__MANIFEST_LOADING__) != null ? ___MANIFEST_LOADING__ : _target___FEDERATION__4.__MANIFEST_LOADING__ = {};\n    var ___PRELOADED_MAP__;\n    (___PRELOADED_MAP__ = (_target___FEDERATION__5 = target.__FEDERATION__).__PRELOADED_MAP__) != null ? ___PRELOADED_MAP__ : _target___FEDERATION__5.__PRELOADED_MAP__ = new Map();\n}\nsetGlobalDefaultVal(globalThis);\nsetGlobalDefaultVal(nativeGlobal);\nfunction resetFederationGlobalInfo() {\n    globalThis.__FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    globalThis.__FEDERATION__.__INSTANCES__ = [];\n    globalThis.__FEDERATION__.moduleInfo = {};\n    globalThis.__FEDERATION__.__SHARE__ = {};\n    globalThis.__FEDERATION__.__MANIFEST_LOADING__ = {};\n}\nfunction getGlobalFederationInstance(name, version) {\n    const buildId = getBuilderId();\n    return globalThis.__FEDERATION__.__INSTANCES__.find((GMInstance)=>{\n        if (buildId && GMInstance.options.id === getBuilderId()) {\n            return true;\n        }\n        if (GMInstance.options.name === name && !GMInstance.options.version && !version) {\n            return true;\n        }\n        if (GMInstance.options.name === name && version && GMInstance.options.version === version) {\n            return true;\n        }\n        return false;\n    });\n}\nfunction setGlobalFederationInstance(FederationInstance) {\n    globalThis.__FEDERATION__.__INSTANCES__.push(FederationInstance);\n}\nfunction getGlobalFederationConstructor() {\n    return globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR__;\n}\nfunction setGlobalFederationConstructor(FederationConstructor, isDebug = sdk.isDebugMode()) {\n    if (isDebug) {\n        globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR__ = FederationConstructor;\n        globalThis.__FEDERATION__.__DEBUG_CONSTRUCTOR_VERSION__ = \"0.6.16\";\n    }\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction getInfoWithoutType(target, key) {\n    if (typeof key === 'string') {\n        const keyRes = target[key];\n        if (keyRes) {\n            return {\n                value: target[key],\n                key: key\n            };\n        } else {\n            const targetKeys = Object.keys(target);\n            for (const targetKey of targetKeys){\n                const [targetTypeOrName, _] = targetKey.split(':');\n                const nKey = `${targetTypeOrName}:${key}`;\n                const typeWithKeyRes = target[nKey];\n                if (typeWithKeyRes) {\n                    return {\n                        value: typeWithKeyRes,\n                        key: nKey\n                    };\n                }\n            }\n            return {\n                value: undefined,\n                key: key\n            };\n        }\n    } else {\n        throw new Error('key must be string');\n    }\n}\nconst getGlobalSnapshot = ()=>nativeGlobal.__FEDERATION__.moduleInfo;\nconst getTargetSnapshotInfoByModuleInfo = (moduleInfo, snapshot)=>{\n    // Check if the remote is included in the hostSnapshot\n    const moduleKey = getFMId(moduleInfo);\n    const getModuleInfo = getInfoWithoutType(snapshot, moduleKey).value;\n    // The remoteSnapshot might not include a version\n    if (getModuleInfo && !getModuleInfo.version && 'version' in moduleInfo && moduleInfo['version']) {\n        getModuleInfo.version = moduleInfo['version'];\n    }\n    if (getModuleInfo) {\n        return getModuleInfo;\n    }\n    // If the remote is not included in the hostSnapshot, deploy a micro app snapshot\n    if ('version' in moduleInfo && moduleInfo['version']) {\n        const { version } = moduleInfo, resModuleInfo = polyfills._object_without_properties_loose(moduleInfo, [\n            \"version\"\n        ]);\n        const moduleKeyWithoutVersion = getFMId(resModuleInfo);\n        const getModuleInfoWithoutVersion = getInfoWithoutType(nativeGlobal.__FEDERATION__.moduleInfo, moduleKeyWithoutVersion).value;\n        if ((getModuleInfoWithoutVersion == null ? void 0 : getModuleInfoWithoutVersion.version) === version) {\n            return getModuleInfoWithoutVersion;\n        }\n    }\n    return;\n};\nconst getGlobalSnapshotInfoByModuleInfo = (moduleInfo)=>getTargetSnapshotInfoByModuleInfo(moduleInfo, nativeGlobal.__FEDERATION__.moduleInfo);\nconst setGlobalSnapshotInfoByModuleInfo = (remoteInfo, moduleDetailInfo)=>{\n    const moduleKey = getFMId(remoteInfo);\n    nativeGlobal.__FEDERATION__.moduleInfo[moduleKey] = moduleDetailInfo;\n    return nativeGlobal.__FEDERATION__.moduleInfo;\n};\nconst addGlobalSnapshot = (moduleInfos)=>{\n    nativeGlobal.__FEDERATION__.moduleInfo = polyfills._extends({}, nativeGlobal.__FEDERATION__.moduleInfo, moduleInfos);\n    return ()=>{\n        const keys = Object.keys(moduleInfos);\n        for (const key of keys){\n            delete nativeGlobal.__FEDERATION__.moduleInfo[key];\n        }\n    };\n};\nconst getRemoteEntryExports = (name, globalName)=>{\n    const remoteEntryKey = globalName || `__FEDERATION_${name}:custom__`;\n    const entryExports = globalThis[remoteEntryKey];\n    return {\n        remoteEntryKey,\n        entryExports\n    };\n};\n// This function is used to register global plugins.\n// It iterates over the provided plugins and checks if they are already registered.\n// If a plugin is not registered, it is added to the global plugins.\n// If a plugin is already registered, a warning message is logged.\nconst registerGlobalPlugins = (plugins)=>{\n    const { __GLOBAL_PLUGIN__ } = nativeGlobal.__FEDERATION__;\n    plugins.forEach((plugin)=>{\n        if (__GLOBAL_PLUGIN__.findIndex((p)=>p.name === plugin.name) === -1) {\n            __GLOBAL_PLUGIN__.push(plugin);\n        } else {\n            warn(`The plugin ${plugin.name} has been registered.`);\n        }\n    });\n};\nconst getGlobalHostPlugins = ()=>nativeGlobal.__FEDERATION__.__GLOBAL_PLUGIN__;\nconst getPreloaded = (id)=>globalThis.__FEDERATION__.__PRELOADED_MAP__.get(id);\nconst setPreloaded = (id)=>globalThis.__FEDERATION__.__PRELOADED_MAP__.set(id, true);\n\nconst DEFAULT_SCOPE = 'default';\nconst DEFAULT_REMOTE_TYPE = 'global';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// those constants are based on https://www.rubydoc.info/gems/semantic_range/3.0.0/SemanticRange#BUILDIDENTIFIER-constant\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nconst buildIdentifier = '[0-9A-Za-z-]+';\nconst build = `(?:\\\\+(${buildIdentifier}(?:\\\\.${buildIdentifier})*))`;\nconst numericIdentifier = '0|[1-9]\\\\d*';\nconst numericIdentifierLoose = '[0-9]+';\nconst nonNumericIdentifier = '\\\\d*[a-zA-Z-][a-zA-Z0-9-]*';\nconst preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;\nconst preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\\\.${preReleaseIdentifierLoose})*))`;\nconst preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;\nconst preRelease = `(?:-(${preReleaseIdentifier}(?:\\\\.${preReleaseIdentifier})*))`;\nconst xRangeIdentifier = `${numericIdentifier}|x|X|\\\\*`;\nconst xRangePlain = `[v=\\\\s]*(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;\nconst hyphenRange = `^\\\\s*(${xRangePlain})\\\\s+-\\\\s+(${xRangePlain})\\\\s*$`;\nconst mainVersionLoose = `(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})`;\nconst loosePlain = `[v=\\\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;\nconst gtlt = '((?:<|>)?=?)';\nconst comparatorTrim = `(\\\\s*)${gtlt}\\\\s*(${loosePlain}|${xRangePlain})`;\nconst loneTilde = '(?:~>?)';\nconst tildeTrim = `(\\\\s*)${loneTilde}\\\\s+`;\nconst loneCaret = '(?:\\\\^)';\nconst caretTrim = `(\\\\s*)${loneCaret}\\\\s+`;\nconst star = '(<|>)?=?\\\\s*\\\\*';\nconst caret = `^${loneCaret}${xRangePlain}$`;\nconst mainVersion = `(${numericIdentifier})\\\\.(${numericIdentifier})\\\\.(${numericIdentifier})`;\nconst fullPlain = `v?${mainVersion}${preRelease}?${build}?`;\nconst tilde = `^${loneTilde}${xRangePlain}$`;\nconst xRange = `^${gtlt}\\\\s*${xRangePlain}$`;\nconst comparator = `^${gtlt}\\\\s*(${fullPlain})$|^$`;\n// copy from semver package\nconst gte0 = '^\\\\s*>=\\\\s*0.0.0\\\\s*$';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseRegex(source) {\n    return new RegExp(source);\n}\nfunction isXVersion(version) {\n    return !version || version.toLowerCase() === 'x' || version === '*';\n}\nfunction pipe(...fns) {\n    return (x)=>fns.reduce((v, f)=>f(v), x);\n}\nfunction extractComparator(comparatorString) {\n    return comparatorString.match(parseRegex(comparator));\n}\nfunction combineVersion(major, minor, patch, preRelease) {\n    const mainVersion = `${major}.${minor}.${patch}`;\n    if (preRelease) {\n        return `${mainVersion}-${preRelease}`;\n    }\n    return mainVersion;\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseHyphen(range) {\n    return range.replace(parseRegex(hyphenRange), (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease)=>{\n        if (isXVersion(fromMajor)) {\n            from = '';\n        } else if (isXVersion(fromMinor)) {\n            from = `>=${fromMajor}.0.0`;\n        } else if (isXVersion(fromPatch)) {\n            from = `>=${fromMajor}.${fromMinor}.0`;\n        } else {\n            from = `>=${from}`;\n        }\n        if (isXVersion(toMajor)) {\n            to = '';\n        } else if (isXVersion(toMinor)) {\n            to = `<${Number(toMajor) + 1}.0.0-0`;\n        } else if (isXVersion(toPatch)) {\n            to = `<${toMajor}.${Number(toMinor) + 1}.0-0`;\n        } else if (toPreRelease) {\n            to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;\n        } else {\n            to = `<=${to}`;\n        }\n        return `${from} ${to}`.trim();\n    });\n}\nfunction parseComparatorTrim(range) {\n    return range.replace(parseRegex(comparatorTrim), '$1$2$3');\n}\nfunction parseTildeTrim(range) {\n    return range.replace(parseRegex(tildeTrim), '$1~');\n}\nfunction parseCaretTrim(range) {\n    return range.replace(parseRegex(caretTrim), '$1^');\n}\nfunction parseCarets(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(caret), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                if (major === '0') {\n                    return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n                } else {\n                    return `>=${major}.${minor}.0 <${Number(major) + 1}.0.0-0`;\n                }\n            } else if (preRelease) {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                } else {\n                    return `>=${major}.${minor}.${patch}-${preRelease} <${Number(major) + 1}.0.0-0`;\n                }\n            } else {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                }\n                return `>=${major}.${minor}.${patch} <${Number(major) + 1}.0.0-0`;\n            }\n        })).join(' ');\n}\nfunction parseTildes(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(tilde), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n            } else if (preRelease) {\n                return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n        })).join(' ');\n}\nfunction parseXRanges(range) {\n    return range.split(/\\s+/).map((rangeVersion)=>rangeVersion.trim().replace(parseRegex(xRange), (ret, gtlt, major, minor, patch, preRelease)=>{\n            const isXMajor = isXVersion(major);\n            const isXMinor = isXMajor || isXVersion(minor);\n            const isXPatch = isXMinor || isXVersion(patch);\n            if (gtlt === '=' && isXPatch) {\n                gtlt = '';\n            }\n            preRelease = '';\n            if (isXMajor) {\n                if (gtlt === '>' || gtlt === '<') {\n                    // nothing is allowed\n                    return '<0.0.0-0';\n                } else {\n                    // nothing is forbidden\n                    return '*';\n                }\n            } else if (gtlt && isXPatch) {\n                // replace X with 0\n                if (isXMinor) {\n                    minor = 0;\n                }\n                patch = 0;\n                if (gtlt === '>') {\n                    // >1 => >=2.0.0\n                    // >1.2 => >=1.3.0\n                    gtlt = '>=';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                        minor = 0;\n                        patch = 0;\n                    } else {\n                        minor = Number(minor) + 1;\n                        patch = 0;\n                    }\n                } else if (gtlt === '<=') {\n                    // <=0.7.x is actually <0.8.0, since any 0.7.x should pass\n                    // Similarly, <=7.x is actually <8.0.0, etc.\n                    gtlt = '<';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                    } else {\n                        minor = Number(minor) + 1;\n                    }\n                }\n                if (gtlt === '<') {\n                    preRelease = '-0';\n                }\n                return `${gtlt + major}.${minor}.${patch}${preRelease}`;\n            } else if (isXMinor) {\n                return `>=${major}.0.0${preRelease} <${Number(major) + 1}.0.0-0`;\n            } else if (isXPatch) {\n                return `>=${major}.${minor}.0${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return ret;\n        })).join(' ');\n}\nfunction parseStar(range) {\n    return range.trim().replace(parseRegex(star), '');\n}\nfunction parseGTE0(comparatorString) {\n    return comparatorString.trim().replace(parseRegex(gte0), '');\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction compareAtom(rangeAtom, versionAtom) {\n    rangeAtom = Number(rangeAtom) || rangeAtom;\n    versionAtom = Number(versionAtom) || versionAtom;\n    if (rangeAtom > versionAtom) {\n        return 1;\n    }\n    if (rangeAtom === versionAtom) {\n        return 0;\n    }\n    return -1;\n}\nfunction comparePreRelease(rangeAtom, versionAtom) {\n    const { preRelease: rangePreRelease } = rangeAtom;\n    const { preRelease: versionPreRelease } = versionAtom;\n    if (rangePreRelease === undefined && Boolean(versionPreRelease)) {\n        return 1;\n    }\n    if (Boolean(rangePreRelease) && versionPreRelease === undefined) {\n        return -1;\n    }\n    if (rangePreRelease === undefined && versionPreRelease === undefined) {\n        return 0;\n    }\n    for(let i = 0, n = rangePreRelease.length; i <= n; i++){\n        const rangeElement = rangePreRelease[i];\n        const versionElement = versionPreRelease[i];\n        if (rangeElement === versionElement) {\n            continue;\n        }\n        if (rangeElement === undefined && versionElement === undefined) {\n            return 0;\n        }\n        if (!rangeElement) {\n            return 1;\n        }\n        if (!versionElement) {\n            return -1;\n        }\n        return compareAtom(rangeElement, versionElement);\n    }\n    return 0;\n}\nfunction compareVersion(rangeAtom, versionAtom) {\n    return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);\n}\nfunction eq(rangeAtom, versionAtom) {\n    return rangeAtom.version === versionAtom.version;\n}\nfunction compare(rangeAtom, versionAtom) {\n    switch(rangeAtom.operator){\n        case '':\n        case '=':\n            return eq(rangeAtom, versionAtom);\n        case '>':\n            return compareVersion(rangeAtom, versionAtom) < 0;\n        case '>=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;\n        case '<':\n            return compareVersion(rangeAtom, versionAtom) > 0;\n        case '<=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;\n        case undefined:\n            {\n                // mean * or x -> all versions\n                return true;\n            }\n        default:\n            return false;\n    }\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseComparatorString(range) {\n    return pipe(// handle caret\n    // ^ --> * (any, kinda silly)\n    // ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n    // ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n    // ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n    // ^1.2.3 --> >=1.2.3 <2.0.0-0\n    // ^1.2.0 --> >=1.2.0 <2.0.0-0\n    parseCarets, // handle tilde\n    // ~, ~> --> * (any, kinda silly)\n    // ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n    // ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n    // ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n    // ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n    // ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n    parseTildes, parseXRanges, parseStar)(range);\n}\nfunction parseRange(range) {\n    return pipe(// handle hyphenRange\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    parseHyphen, // handle trim comparator\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    parseComparatorTrim, // handle trim tilde\n    // `~ 1.2.3` => `~1.2.3`\n    parseTildeTrim, // handle trim caret\n    // `^ 1.2.3` => `^1.2.3`\n    parseCaretTrim)(range.trim()).split(/\\s+/).join(' ');\n}\nfunction satisfy(version, range) {\n    if (!version) {\n        return false;\n    }\n    const parsedRange = parseRange(range);\n    const parsedComparator = parsedRange.split(' ').map((rangeVersion)=>parseComparatorString(rangeVersion)).join(' ');\n    const comparators = parsedComparator.split(/\\s+/).map((comparator)=>parseGTE0(comparator));\n    const extractedVersion = extractComparator(version);\n    if (!extractedVersion) {\n        return false;\n    }\n    const [, versionOperator, , versionMajor, versionMinor, versionPatch, versionPreRelease] = extractedVersion;\n    const versionAtom = {\n        operator: versionOperator,\n        version: combineVersion(versionMajor, versionMinor, versionPatch, versionPreRelease),\n        major: versionMajor,\n        minor: versionMinor,\n        patch: versionPatch,\n        preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split('.')\n    };\n    for (const comparator of comparators){\n        const extractedComparator = extractComparator(comparator);\n        if (!extractedComparator) {\n            return false;\n        }\n        const [, rangeOperator, , rangeMajor, rangeMinor, rangePatch, rangePreRelease] = extractedComparator;\n        const rangeAtom = {\n            operator: rangeOperator,\n            version: combineVersion(rangeMajor, rangeMinor, rangePatch, rangePreRelease),\n            major: rangeMajor,\n            minor: rangeMinor,\n            patch: rangePatch,\n            preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split('.')\n        };\n        if (!compare(rangeAtom, versionAtom)) {\n            return false; // early return\n        }\n    }\n    return true;\n}\n\nfunction formatShare(shareArgs, from, name, shareStrategy) {\n    let get;\n    if ('get' in shareArgs) {\n        // eslint-disable-next-line prefer-destructuring\n        get = shareArgs.get;\n    } else if ('lib' in shareArgs) {\n        get = ()=>Promise.resolve(shareArgs.lib);\n    } else {\n        get = ()=>Promise.resolve(()=>{\n                throw new Error(`Can not get shared '${name}'!`);\n            });\n    }\n    if (shareArgs.strategy) {\n        warn(`\"shared.strategy is deprecated, please set in initOptions.shareStrategy instead!\"`);\n    }\n    var _shareArgs_version, _shareArgs_scope, _shareArgs_strategy;\n    return polyfills._extends({\n        deps: [],\n        useIn: [],\n        from,\n        loading: null\n    }, shareArgs, {\n        shareConfig: polyfills._extends({\n            requiredVersion: `^${shareArgs.version}`,\n            singleton: false,\n            eager: false,\n            strictVersion: false\n        }, shareArgs.shareConfig),\n        get,\n        loaded: (shareArgs == null ? void 0 : shareArgs.loaded) || 'lib' in shareArgs ? true : undefined,\n        version: (_shareArgs_version = shareArgs.version) != null ? _shareArgs_version : '0',\n        scope: Array.isArray(shareArgs.scope) ? shareArgs.scope : [\n            (_shareArgs_scope = shareArgs.scope) != null ? _shareArgs_scope : 'default'\n        ],\n        strategy: ((_shareArgs_strategy = shareArgs.strategy) != null ? _shareArgs_strategy : shareStrategy) || 'version-first'\n    });\n}\nfunction formatShareConfigs(globalOptions, userOptions) {\n    const shareArgs = userOptions.shared || {};\n    const from = userOptions.name;\n    const shareInfos = Object.keys(shareArgs).reduce((res, pkgName)=>{\n        const arrayShareArgs = arrayOptions(shareArgs[pkgName]);\n        res[pkgName] = res[pkgName] || [];\n        arrayShareArgs.forEach((shareConfig)=>{\n            res[pkgName].push(formatShare(shareConfig, from, pkgName, userOptions.shareStrategy));\n        });\n        return res;\n    }, {});\n    const shared = polyfills._extends({}, globalOptions.shared);\n    Object.keys(shareInfos).forEach((shareKey)=>{\n        if (!shared[shareKey]) {\n            shared[shareKey] = shareInfos[shareKey];\n        } else {\n            shareInfos[shareKey].forEach((newUserSharedOptions)=>{\n                const isSameVersion = shared[shareKey].find((sharedVal)=>sharedVal.version === newUserSharedOptions.version);\n                if (!isSameVersion) {\n                    shared[shareKey].push(newUserSharedOptions);\n                }\n            });\n        }\n    });\n    return {\n        shared,\n        shareInfos\n    };\n}\nfunction versionLt(a, b) {\n    const transformInvalidVersion = (version)=>{\n        const isNumberVersion = !Number.isNaN(Number(version));\n        if (isNumberVersion) {\n            const splitArr = version.split('.');\n            let validVersion = version;\n            for(let i = 0; i < 3 - splitArr.length; i++){\n                validVersion += '.0';\n            }\n            return validVersion;\n        }\n        return version;\n    };\n    if (satisfy(transformInvalidVersion(a), `<=${transformInvalidVersion(b)}`)) {\n        return true;\n    } else {\n        return false;\n    }\n}\nconst findVersion = (shareVersionMap, cb)=>{\n    const callback = cb || function(prev, cur) {\n        return versionLt(prev, cur);\n    };\n    return Object.keys(shareVersionMap).reduce((prev, cur)=>{\n        if (!prev) {\n            return cur;\n        }\n        if (callback(prev, cur)) {\n            return cur;\n        }\n        // default version is '0' https://github.com/webpack/webpack/blob/main/lib/sharing/ProvideSharedModule.js#L136\n        if (prev === '0') {\n            return cur;\n        }\n        return prev;\n    }, 0);\n};\nconst isLoaded = (shared)=>{\n    return Boolean(shared.loaded) || typeof shared.lib === 'function';\n};\nfunction findSingletonVersionOrderByVersion(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        return !isLoaded(versions[prev]) && versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction findSingletonVersionOrderByLoaded(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        if (isLoaded(versions[cur])) {\n            if (isLoaded(versions[prev])) {\n                return Boolean(versionLt(prev, cur));\n            } else {\n                return true;\n            }\n        }\n        if (isLoaded(versions[prev])) {\n            return false;\n        }\n        return versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction getFindShareFunction(strategy) {\n    if (strategy === 'loaded-first') {\n        return findSingletonVersionOrderByLoaded;\n    }\n    return findSingletonVersionOrderByVersion;\n}\nfunction getRegisteredShare(localShareScopeMap, pkgName, shareInfo, resolveShare) {\n    if (!localShareScopeMap) {\n        return;\n    }\n    const { shareConfig, scope = DEFAULT_SCOPE, strategy } = shareInfo;\n    const scopes = Array.isArray(scope) ? scope : [\n        scope\n    ];\n    for (const sc of scopes){\n        if (shareConfig && localShareScopeMap[sc] && localShareScopeMap[sc][pkgName]) {\n            const { requiredVersion } = shareConfig;\n            const findShareFunction = getFindShareFunction(strategy);\n            const maxOrSingletonVersion = findShareFunction(localShareScopeMap, sc, pkgName);\n            //@ts-ignore\n            const defaultResolver = ()=>{\n                if (shareConfig.singleton) {\n                    if (typeof requiredVersion === 'string' && !satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        const msg = `Version ${maxOrSingletonVersion} from ${maxOrSingletonVersion && localShareScopeMap[sc][pkgName][maxOrSingletonVersion].from} of shared singleton module ${pkgName} does not satisfy the requirement of ${shareInfo.from} which needs ${requiredVersion})`;\n                        if (shareConfig.strictVersion) {\n                            error(msg);\n                        } else {\n                            warn(msg);\n                        }\n                    }\n                    return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                } else {\n                    if (requiredVersion === false || requiredVersion === '*') {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    if (satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    for (const [versionKey, versionValue] of Object.entries(localShareScopeMap[sc][pkgName])){\n                        if (satisfy(versionKey, requiredVersion)) {\n                            return versionValue;\n                        }\n                    }\n                }\n            };\n            const params = {\n                shareScopeMap: localShareScopeMap,\n                scope: sc,\n                pkgName,\n                version: maxOrSingletonVersion,\n                GlobalFederation: Global.__FEDERATION__,\n                resolver: defaultResolver\n            };\n            const resolveShared = resolveShare.emit(params) || params;\n            return resolveShared.resolver();\n        }\n    }\n}\nfunction getGlobalShareScope() {\n    return Global.__FEDERATION__.__SHARE__;\n}\nfunction getTargetSharedOptions(options) {\n    const { pkgName, extraOptions, shareInfos } = options;\n    const defaultResolver = (sharedOptions)=>{\n        if (!sharedOptions) {\n            return undefined;\n        }\n        const shareVersionMap = {};\n        sharedOptions.forEach((shared)=>{\n            shareVersionMap[shared.version] = shared;\n        });\n        const callback = function(prev, cur) {\n            return !isLoaded(shareVersionMap[prev]) && versionLt(prev, cur);\n        };\n        const maxVersion = findVersion(shareVersionMap, callback);\n        return shareVersionMap[maxVersion];\n    };\n    var _extraOptions_resolver;\n    const resolver = (_extraOptions_resolver = extraOptions == null ? void 0 : extraOptions.resolver) != null ? _extraOptions_resolver : defaultResolver;\n    return Object.assign({}, resolver(shareInfos[pkgName]), extraOptions == null ? void 0 : extraOptions.customShareInfo);\n}\n\nexports.DEFAULT_REMOTE_TYPE = DEFAULT_REMOTE_TYPE;\nexports.DEFAULT_SCOPE = DEFAULT_SCOPE;\nexports.Global = Global;\nexports.addGlobalSnapshot = addGlobalSnapshot;\nexports.addUniqueItem = addUniqueItem;\nexports.arrayOptions = arrayOptions;\nexports.assert = assert;\nexports.error = error;\nexports.formatShareConfigs = formatShareConfigs;\nexports.getBuilderId = getBuilderId;\nexports.getFMId = getFMId;\nexports.getGlobalFederationConstructor = getGlobalFederationConstructor;\nexports.getGlobalFederationInstance = getGlobalFederationInstance;\nexports.getGlobalHostPlugins = getGlobalHostPlugins;\nexports.getGlobalShareScope = getGlobalShareScope;\nexports.getGlobalSnapshot = getGlobalSnapshot;\nexports.getGlobalSnapshotInfoByModuleInfo = getGlobalSnapshotInfoByModuleInfo;\nexports.getInfoWithoutType = getInfoWithoutType;\nexports.getPreloaded = getPreloaded;\nexports.getRegisteredShare = getRegisteredShare;\nexports.getRemoteEntryExports = getRemoteEntryExports;\nexports.getRemoteEntryInfoFromSnapshot = getRemoteEntryInfoFromSnapshot;\nexports.getTargetSharedOptions = getTargetSharedOptions;\nexports.getTargetSnapshotInfoByModuleInfo = getTargetSnapshotInfoByModuleInfo;\nexports.globalLoading = globalLoading;\nexports.isObject = isObject;\nexports.isPlainObject = isPlainObject;\nexports.isPureRemoteEntry = isPureRemoteEntry;\nexports.isRemoteInfoWithEntry = isRemoteInfoWithEntry;\nexports.logger = logger;\nexports.nativeGlobal = nativeGlobal;\nexports.registerGlobalPlugins = registerGlobalPlugins;\nexports.resetFederationGlobalInfo = resetFederationGlobalInfo;\nexports.setGlobalFederationConstructor = setGlobalFederationConstructor;\nexports.setGlobalFederationInstance = setGlobalFederationInstance;\nexports.setGlobalSnapshotInfoByModuleInfo = setGlobalSnapshotInfoByModuleInfo;\nexports.setPreloaded = setPreloaded;\nexports.warn = warn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2214\n\n}");

/***/ }),

/***/ 3070:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3091);\n/* harmony import */ var C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\nif(!__webpack_require__.federation.runtime){\n\tvar prevFederation = __webpack_require__.federation;\n\t__webpack_require__.federation = {}\n\tfor(var key in (C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default())){\n\t\t__webpack_require__.federation[key] = (C_src_react_apps_webpack_node_modules_module_federation_webpack_bundler_runtime_dist_index_cjs_js__WEBPACK_IMPORTED_MODULE_0___default())[key];\n\t}\n\tfor(var key in prevFederation){\n\t\t__webpack_require__.federation[key] = prevFederation[key];\n\t}\n}\nif(!__webpack_require__.federation.instance){\n\n\t__webpack_require__.federation.instance = __webpack_require__.federation.runtime.init(__webpack_require__.federation.initOptions);\n\tif(__webpack_require__.federation.attachShareScopeMap){\n\t\t__webpack_require__.federation.attachShareScopeMap(__webpack_require__)\n\t}\n\tif(__webpack_require__.federation.installInitialConsumes){\n\t\t__webpack_require__.federation.installInitialConsumes()\n\t}\n\n\tif(!__webpack_require__.federation.isMFRemote && __webpack_require__.federation.prefetch){\n\t__webpack_require__.federation.prefetch()\n\t}\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3070\n\n}");

/***/ }),

/***/ 3091:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("{\n\nvar runtime = __webpack_require__(3197);\nvar constant = __webpack_require__(4179);\nvar sdk = __webpack_require__(9468);\nvar polyfills = __webpack_require__(9399);\n\nfunction _interopNamespaceDefault(e) {\n  var n = Object.create(null);\n  if (e) {\n    Object.keys(e).forEach(function (k) {\n      if (k !== 'default') {\n        var d = Object.getOwnPropertyDescriptor(e, k);\n        Object.defineProperty(n, k, d.get ? d : {\n          enumerable: true,\n          get: function () { return e[k]; }\n        });\n      }\n    });\n  }\n  n.default = e;\n  return Object.freeze(n);\n}\n\nvar runtime__namespace = /*#__PURE__*/_interopNamespaceDefault(runtime);\n\nfunction attachShareScopeMap(webpackRequire) {\n    if (!webpackRequire.S || webpackRequire.federation.hasAttachShareScopeMap || !webpackRequire.federation.instance || !webpackRequire.federation.instance.shareScopeMap) {\n        return;\n    }\n    webpackRequire.S = webpackRequire.federation.instance.shareScopeMap;\n    webpackRequire.federation.hasAttachShareScopeMap = true;\n}\n\nfunction remotes(options) {\n    const { chunkId, promises, chunkMapping, idToExternalAndNameMapping, webpackRequire, idToRemoteMap } = options;\n    attachShareScopeMap(webpackRequire);\n    if (webpackRequire.o(chunkMapping, chunkId)) {\n        chunkMapping[chunkId].forEach((id)=>{\n            let getScope = webpackRequire.R;\n            if (!getScope) {\n                getScope = [];\n            }\n            const data = idToExternalAndNameMapping[id];\n            const remoteInfos = idToRemoteMap[id];\n            // @ts-ignore seems not work\n            if (getScope.indexOf(data) >= 0) {\n                return;\n            }\n            // @ts-ignore seems not work\n            getScope.push(data);\n            if (data.p) {\n                return promises.push(data.p);\n            }\n            const onError = (error)=>{\n                if (!error) {\n                    error = new Error('Container missing');\n                }\n                if (typeof error.message === 'string') {\n                    error.message += `\\nwhile loading \"${data[1]}\" from ${data[2]}`;\n                }\n                webpackRequire.m[id] = ()=>{\n                    throw error;\n                };\n                data.p = 0;\n            };\n            const handleFunction = (fn, arg1, arg2, d, next, first)=>{\n                try {\n                    const promise = fn(arg1, arg2);\n                    if (promise && promise.then) {\n                        const p = promise.then((result)=>next(result, d), onError);\n                        if (first) {\n                            promises.push(data.p = p);\n                        } else {\n                            return p;\n                        }\n                    } else {\n                        return next(promise, d, first);\n                    }\n                } catch (error) {\n                    onError(error);\n                }\n            };\n            const onExternal = (external, _, first)=>external ? handleFunction(webpackRequire.I, data[0], 0, external, onInitialized, first) : onError();\n            // eslint-disable-next-line no-var\n            var onInitialized = (_, external, first)=>handleFunction(external.get, data[1], getScope, 0, onFactory, first);\n            // eslint-disable-next-line no-var\n            var onFactory = (factory)=>{\n                data.p = 1;\n                webpackRequire.m[id] = (module)=>{\n                    module.exports = factory();\n                };\n            };\n            const onRemoteLoaded = ()=>{\n                try {\n                    const remoteName = sdk.decodeName(remoteInfos[0].name, sdk.ENCODE_NAME_PREFIX);\n                    const remoteModuleName = remoteName + data[1].slice(1);\n                    return webpackRequire.federation.instance.loadRemote(remoteModuleName, {\n                        loadFactory: false,\n                        from: 'build'\n                    });\n                } catch (error) {\n                    onError(error);\n                }\n            };\n            const useRuntimeLoad = remoteInfos.length === 1 && constant.FEDERATION_SUPPORTED_TYPES.includes(remoteInfos[0].externalType) && remoteInfos[0].name;\n            if (useRuntimeLoad) {\n                handleFunction(onRemoteLoaded, data[2], 0, 0, onFactory, 1);\n            } else {\n                handleFunction(webpackRequire, data[2], 0, 0, onExternal, 1);\n            }\n        });\n    }\n}\n\nfunction consumes(options) {\n    const { chunkId, promises, chunkMapping, installedModules, moduleToHandlerMapping, webpackRequire } = options;\n    attachShareScopeMap(webpackRequire);\n    if (webpackRequire.o(chunkMapping, chunkId)) {\n        chunkMapping[chunkId].forEach((id)=>{\n            if (webpackRequire.o(installedModules, id)) {\n                return promises.push(installedModules[id]);\n            }\n            const onFactory = (factory)=>{\n                installedModules[id] = 0;\n                webpackRequire.m[id] = (module)=>{\n                    delete webpackRequire.c[id];\n                    module.exports = factory();\n                };\n            };\n            const onError = (error)=>{\n                delete installedModules[id];\n                webpackRequire.m[id] = (module)=>{\n                    delete webpackRequire.c[id];\n                    throw error;\n                };\n            };\n            try {\n                const federationInstance = webpackRequire.federation.instance;\n                if (!federationInstance) {\n                    throw new Error('Federation instance not found!');\n                }\n                const { shareKey, getter, shareInfo } = moduleToHandlerMapping[id];\n                const promise = federationInstance.loadShare(shareKey, {\n                    customShareInfo: shareInfo\n                }).then((factory)=>{\n                    if (factory === false) {\n                        return getter();\n                    }\n                    return factory;\n                });\n                if (promise.then) {\n                    promises.push(installedModules[id] = promise.then(onFactory).catch(onError));\n                } else {\n                    // @ts-ignore maintain previous logic\n                    onFactory(promise);\n                }\n            } catch (e) {\n                onError(e);\n            }\n        });\n    }\n}\n\nfunction initializeSharing({ shareScopeName, webpackRequire, initPromises, initTokens, initScope }) {\n    if (!initScope) initScope = [];\n    const mfInstance = webpackRequire.federation.instance;\n    // handling circular init calls\n    var initToken = initTokens[shareScopeName];\n    if (!initToken) initToken = initTokens[shareScopeName] = {\n        from: mfInstance.name\n    };\n    if (initScope.indexOf(initToken) >= 0) return;\n    initScope.push(initToken);\n    const promise = initPromises[shareScopeName];\n    if (promise) return promise;\n    var warn = (msg)=>typeof console !== 'undefined' && console.warn && console.warn(msg);\n    var initExternal = (id)=>{\n        var handleError = (err)=>warn('Initialization of sharing external failed: ' + err);\n        try {\n            var module = webpackRequire(id);\n            if (!module) return;\n            var initFn = (module)=>module && module.init && // @ts-ignore compat legacy mf shared behavior\n                module.init(webpackRequire.S[shareScopeName], initScope);\n            if (module.then) return promises.push(module.then(initFn, handleError));\n            var initResult = initFn(module);\n            // @ts-ignore\n            if (initResult && typeof initResult !== 'boolean' && initResult.then) // @ts-ignore\n            return promises.push(initResult['catch'](handleError));\n        } catch (err) {\n            handleError(err);\n        }\n    };\n    const promises = mfInstance.initializeSharing(shareScopeName, {\n        strategy: mfInstance.options.shareStrategy,\n        initScope,\n        from: 'build'\n    });\n    attachShareScopeMap(webpackRequire);\n    const bundlerRuntimeRemotesOptions = webpackRequire.federation.bundlerRuntimeOptions.remotes;\n    if (bundlerRuntimeRemotesOptions) {\n        Object.keys(bundlerRuntimeRemotesOptions.idToRemoteMap).forEach((moduleId)=>{\n            const info = bundlerRuntimeRemotesOptions.idToRemoteMap[moduleId];\n            const externalModuleId = bundlerRuntimeRemotesOptions.idToExternalAndNameMapping[moduleId][2];\n            if (info.length > 1) {\n                initExternal(externalModuleId);\n            } else if (info.length === 1) {\n                const remoteInfo = info[0];\n                if (!constant.FEDERATION_SUPPORTED_TYPES.includes(remoteInfo.externalType)) {\n                    initExternal(externalModuleId);\n                }\n            }\n        });\n    }\n    if (!promises.length) {\n        return initPromises[shareScopeName] = true;\n    }\n    return initPromises[shareScopeName] = Promise.all(promises).then(()=>initPromises[shareScopeName] = true);\n}\n\nfunction handleInitialConsumes(options) {\n    const { moduleId, moduleToHandlerMapping, webpackRequire } = options;\n    const federationInstance = webpackRequire.federation.instance;\n    if (!federationInstance) {\n        throw new Error('Federation instance not found!');\n    }\n    const { shareKey, shareInfo } = moduleToHandlerMapping[moduleId];\n    try {\n        return federationInstance.loadShareSync(shareKey, {\n            customShareInfo: shareInfo\n        });\n    } catch (err) {\n        console.error('loadShareSync failed! The function should not be called unless you set \"eager:true\". If you do not set it, and encounter this issue, you can check whether an async boundary is implemented.');\n        console.error('The original error message is as follows: ');\n        throw err;\n    }\n}\nfunction installInitialConsumes(options) {\n    const { moduleToHandlerMapping, webpackRequire, installedModules, initialConsumes } = options;\n    initialConsumes.forEach((id)=>{\n        webpackRequire.m[id] = (module)=>{\n            // Handle scenario when module is used synchronously\n            installedModules[id] = 0;\n            delete webpackRequire.c[id];\n            const factory = handleInitialConsumes({\n                moduleId: id,\n                moduleToHandlerMapping,\n                webpackRequire\n            });\n            if (typeof factory !== 'function') {\n                throw new Error(`Shared module is not available for eager consumption: ${id}`);\n            }\n            module.exports = factory();\n        };\n    });\n}\n\nfunction initContainerEntry(options) {\n    const { webpackRequire, shareScope, initScope, shareScopeKey, remoteEntryInitOptions } = options;\n    if (!webpackRequire.S) return;\n    if (!webpackRequire.federation || !webpackRequire.federation.instance || !webpackRequire.federation.initOptions) return;\n    const federationInstance = webpackRequire.federation.instance;\n    var name = shareScopeKey || 'default';\n    federationInstance.initOptions(polyfills._extends({\n        name: webpackRequire.federation.initOptions.name,\n        remotes: []\n    }, remoteEntryInitOptions));\n    federationInstance.initShareScopeMap(name, shareScope, {\n        hostShareScopeMap: (remoteEntryInitOptions == null ? void 0 : remoteEntryInitOptions.shareScopeMap) || {}\n    });\n    if (webpackRequire.federation.attachShareScopeMap) {\n        webpackRequire.federation.attachShareScopeMap(webpackRequire);\n    }\n    if (typeof webpackRequire.federation.prefetch === 'function') {\n        webpackRequire.federation.prefetch();\n    }\n    // @ts-ignore\n    return webpackRequire.I(name, initScope);\n}\n\nconst federation = {\n    runtime: runtime__namespace,\n    instance: undefined,\n    initOptions: undefined,\n    bundlerRuntime: {\n        remotes,\n        consumes,\n        I: initializeSharing,\n        S: {},\n        installInitialConsumes,\n        initContainerEntry\n    },\n    attachShareScopeMap,\n    bundlerRuntimeOptions: {}\n};\n\nmodule.exports = federation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3091\n\n}");

/***/ }),

/***/ 3197:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar polyfills = __webpack_require__(6689);\nvar sdk = __webpack_require__(9468);\nvar share = __webpack_require__(2214);\nvar errorCodes = __webpack_require__(3767);\n\n// Function to match a remote with its name and expose\n// id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n// id: alias(app1) + expose(button) = app1/button\n// id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\nfunction matchRemoteWithNameAndExpose(remotes, id) {\n    for (const remote of remotes){\n        // match pkgName\n        const isNameMatched = id.startsWith(remote.name);\n        let expose = id.replace(remote.name, '');\n        if (isNameMatched) {\n            if (expose.startsWith('/')) {\n                const pkgNameOrAlias = remote.name;\n                expose = `.${expose}`;\n                return {\n                    pkgNameOrAlias,\n                    expose,\n                    remote\n                };\n            } else if (expose === '') {\n                return {\n                    pkgNameOrAlias: remote.name,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n        // match alias\n        const isAliasMatched = remote.alias && id.startsWith(remote.alias);\n        let exposeWithAlias = remote.alias && id.replace(remote.alias, '');\n        if (remote.alias && isAliasMatched) {\n            if (exposeWithAlias && exposeWithAlias.startsWith('/')) {\n                const pkgNameOrAlias = remote.alias;\n                exposeWithAlias = `.${exposeWithAlias}`;\n                return {\n                    pkgNameOrAlias,\n                    expose: exposeWithAlias,\n                    remote\n                };\n            } else if (exposeWithAlias === '') {\n                return {\n                    pkgNameOrAlias: remote.alias,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n    }\n    return;\n}\n// Function to match a remote with its name or alias\nfunction matchRemote(remotes, nameOrAlias) {\n    for (const remote of remotes){\n        const isNameMatched = nameOrAlias === remote.name;\n        if (isNameMatched) {\n            return remote;\n        }\n        const isAliasMatched = remote.alias && nameOrAlias === remote.alias;\n        if (isAliasMatched) {\n            return remote;\n        }\n    }\n    return;\n}\n\nfunction registerPlugins$1(plugins, hookInstances) {\n    const globalPlugins = share.getGlobalHostPlugins();\n    // Incorporate global plugins\n    if (globalPlugins.length > 0) {\n        globalPlugins.forEach((plugin)=>{\n            if (plugins == null ? void 0 : plugins.find((item)=>item.name !== plugin.name)) {\n                plugins.push(plugin);\n            }\n        });\n    }\n    if (plugins && plugins.length > 0) {\n        plugins.forEach((plugin)=>{\n            hookInstances.forEach((hookInstance)=>{\n                hookInstance.applyPlugin(plugin);\n            });\n        });\n    }\n    return plugins;\n}\n\nasync function loadEsmEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                import(/* webpackIgnore: true */ entry).then(resolve).catch(reject);\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nasync function loadSystemJsEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                //@ts-ignore\n                if (false) // removed by dead control flow\n{} else {\n                    new Function('callbacks', `System.import(\"${entry}\").then(callbacks[0]).catch(callbacks[1])`)([\n                        resolve,\n                        reject\n                    ]);\n                }\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nasync function loadEntryScript({ name, globalName, entry, createScriptHook }) {\n    const { entryExports: remoteEntryExports } = share.getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScript(entry, {\n        attrs: {},\n        createScriptHook: (url, attrs)=>{\n            const res = createScriptHook.emit({\n                url,\n                attrs\n            });\n            if (!res) return;\n            if (res instanceof HTMLScriptElement) {\n                return res;\n            }\n            if ('script' in res || 'timeout' in res) {\n                return res;\n            }\n            return;\n        }\n    }).then(()=>{\n        const { remoteEntryKey, entryExports } = share.getRemoteEntryExports(name, globalName);\n        share.assert(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {\n            remoteName: name,\n            remoteEntryUrl: entry,\n            remoteEntryKey\n        }));\n        return entryExports;\n    }).catch((e)=>{\n        throw e;\n    });\n}\nasync function loadEntryDom({ remoteInfo, remoteEntryExports, createScriptHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    switch(type){\n        case 'esm':\n        case 'module':\n            return loadEsmEntry({\n                entry,\n                remoteEntryExports\n            });\n        case 'system':\n            return loadSystemJsEntry({\n                entry,\n                remoteEntryExports\n            });\n        default:\n            return loadEntryScript({\n                entry,\n                globalName,\n                name,\n                createScriptHook\n            });\n    }\n}\nasync function loadEntryNode({ remoteInfo, createScriptHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    const { entryExports: remoteEntryExports } = share.getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScriptNode(entry, {\n        attrs: {\n            name,\n            globalName,\n            type\n        },\n        createScriptHook: (url, attrs)=>{\n            const res = createScriptHook.emit({\n                url,\n                attrs\n            });\n            if (!res) return;\n            if ('url' in res) {\n                return res;\n            }\n            return;\n        }\n    }).then(()=>{\n        const { remoteEntryKey, entryExports } = share.getRemoteEntryExports(name, globalName);\n        share.assert(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {\n            remoteName: name,\n            remoteEntryUrl: entry,\n            remoteEntryKey\n        }));\n        return entryExports;\n    }).catch((e)=>{\n        throw e;\n    });\n}\nfunction getRemoteEntryUniqueKey(remoteInfo) {\n    const { entry, name } = remoteInfo;\n    return sdk.composeKeyWithSeparator(name, entry);\n}\nasync function getRemoteEntry({ origin, remoteEntryExports, remoteInfo }) {\n    const uniqueKey = getRemoteEntryUniqueKey(remoteInfo);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    if (!share.globalLoading[uniqueKey]) {\n        const loadEntryHook = origin.remoteHandler.hooks.lifecycle.loadEntry;\n        const createScriptHook = origin.loaderHook.lifecycle.createScript;\n        share.globalLoading[uniqueKey] = loadEntryHook.emit({\n            createScriptHook,\n            remoteInfo,\n            remoteEntryExports\n        }).then((res)=>{\n            if (res) {\n                return res;\n            }\n            return sdk.isBrowserEnv() ? loadEntryDom({\n                remoteInfo,\n                remoteEntryExports,\n                createScriptHook\n            }) : loadEntryNode({\n                remoteInfo,\n                createScriptHook\n            });\n        });\n    }\n    return share.globalLoading[uniqueKey];\n}\nfunction getRemoteInfo(remote) {\n    return polyfills._extends({}, remote, {\n        entry: 'entry' in remote ? remote.entry : '',\n        type: remote.type || share.DEFAULT_REMOTE_TYPE,\n        entryGlobalName: remote.entryGlobalName || remote.name,\n        shareScope: remote.shareScope || share.DEFAULT_SCOPE\n    });\n}\n\nlet Module = class Module {\n    async getEntry() {\n        if (this.remoteEntryExports) {\n            return this.remoteEntryExports;\n        }\n        // Get remoteEntry.js\n        const remoteEntryExports = await getRemoteEntry({\n            origin: this.host,\n            remoteInfo: this.remoteInfo,\n            remoteEntryExports: this.remoteEntryExports\n        });\n        share.assert(remoteEntryExports, `remoteEntryExports is undefined \\n ${sdk.safeToString(this.remoteInfo)}`);\n        this.remoteEntryExports = remoteEntryExports;\n        return this.remoteEntryExports;\n    }\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    async get(id, expose, options, remoteSnapshot) {\n        const { loadFactory = true } = options || {\n            loadFactory: true\n        };\n        // Get remoteEntry.js\n        const remoteEntryExports = await this.getEntry();\n        if (!this.inited) {\n            const localShareScopeMap = this.host.shareScopeMap;\n            const remoteShareScope = this.remoteInfo.shareScope || 'default';\n            if (!localShareScopeMap[remoteShareScope]) {\n                localShareScopeMap[remoteShareScope] = {};\n            }\n            const shareScope = localShareScopeMap[remoteShareScope];\n            const initScope = [];\n            const remoteEntryInitOptions = {\n                version: this.remoteInfo.version || ''\n            };\n            // Help to find host instance\n            Object.defineProperty(remoteEntryInitOptions, 'shareScopeMap', {\n                value: localShareScopeMap,\n                // remoteEntryInitOptions will be traversed and assigned during container init, ,so this attribute is not allowed to be traversed\n                enumerable: false\n            });\n            const initContainerOptions = await this.host.hooks.lifecycle.beforeInitContainer.emit({\n                shareScope,\n                // @ts-ignore shareScopeMap will be set by Object.defineProperty\n                remoteEntryInitOptions,\n                initScope,\n                remoteInfo: this.remoteInfo,\n                origin: this.host\n            });\n            if (typeof (remoteEntryExports == null ? void 0 : remoteEntryExports.init) === 'undefined') {\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_002, errorCodes.runtimeDescMap, {\n                    remoteName: name,\n                    remoteEntryUrl: this.remoteInfo.entry,\n                    remoteEntryKey: this.remoteInfo.entryGlobalName\n                }));\n            }\n            await remoteEntryExports.init(initContainerOptions.shareScope, initContainerOptions.initScope, initContainerOptions.remoteEntryInitOptions);\n            await this.host.hooks.lifecycle.initContainer.emit(polyfills._extends({}, initContainerOptions, {\n                id,\n                remoteSnapshot,\n                remoteEntryExports\n            }));\n        }\n        this.lib = remoteEntryExports;\n        this.inited = true;\n        let moduleFactory;\n        moduleFactory = await this.host.loaderHook.lifecycle.getModuleFactory.emit({\n            remoteEntryExports,\n            expose,\n            moduleInfo: this.remoteInfo\n        });\n        // get exposeGetter\n        if (!moduleFactory) {\n            moduleFactory = await remoteEntryExports.get(expose);\n        }\n        share.assert(moduleFactory, `${share.getFMId(this.remoteInfo)} remote don't export ${expose}.`);\n        const wrapModuleFactory = this.wraperFactory(moduleFactory, id);\n        if (!loadFactory) {\n            return wrapModuleFactory;\n        }\n        const exposeContent = await wrapModuleFactory();\n        return exposeContent;\n    }\n    wraperFactory(moduleFactory, id) {\n        function defineModuleId(res, id) {\n            if (res && typeof res === 'object' && Object.isExtensible(res) && !Object.getOwnPropertyDescriptor(res, Symbol.for('mf_module_id'))) {\n                Object.defineProperty(res, Symbol.for('mf_module_id'), {\n                    value: id,\n                    enumerable: false\n                });\n            }\n        }\n        if (moduleFactory instanceof Promise) {\n            return async ()=>{\n                const res = await moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        } else {\n            return ()=>{\n                const res = moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        }\n    }\n    constructor({ remoteInfo, host }){\n        this.inited = false;\n        this.lib = undefined;\n        this.remoteInfo = remoteInfo;\n        this.host = host;\n    }\n};\n\nclass SyncHook {\n    on(fn) {\n        if (typeof fn === 'function') {\n            this.listeners.add(fn);\n        }\n    }\n    once(fn) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const self = this;\n        this.on(function wrapper(...args) {\n            self.remove(wrapper);\n            // eslint-disable-next-line prefer-spread\n            return fn.apply(null, args);\n        });\n    }\n    emit(...data) {\n        let result;\n        if (this.listeners.size > 0) {\n            // eslint-disable-next-line prefer-spread\n            this.listeners.forEach((fn)=>{\n                result = fn(...data);\n            });\n        }\n        return result;\n    }\n    remove(fn) {\n        this.listeners.delete(fn);\n    }\n    removeAll() {\n        this.listeners.clear();\n    }\n    constructor(type){\n        this.type = '';\n        this.listeners = new Set();\n        if (type) {\n            this.type = type;\n        }\n    }\n}\n\nclass AsyncHook extends SyncHook {\n    emit(...data) {\n        let result;\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const call = (prev)=>{\n                if (prev === false) {\n                    return false; // Abort process\n                } else if (i < ls.length) {\n                    return Promise.resolve(ls[i++].apply(null, data)).then(call);\n                } else {\n                    return prev;\n                }\n            };\n            result = call();\n        }\n        return Promise.resolve(result);\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction checkReturnData(originalData, returnedData) {\n    if (!share.isObject(returnedData)) {\n        return false;\n    }\n    if (originalData !== returnedData) {\n        // eslint-disable-next-line no-restricted-syntax\n        for(const key in originalData){\n            if (!(key in returnedData)) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nclass SyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!share.isObject(data)) {\n            share.error(`The data for the \"${this.type}\" hook should be an object.`);\n        }\n        for (const fn of this.listeners){\n            try {\n                const tempData = fn(data);\n                if (checkReturnData(data, tempData)) {\n                    data = tempData;\n                } else {\n                    this.onerror(`A plugin returned an unacceptable value for the \"${this.type}\" type.`);\n                    break;\n                }\n            } catch (e) {\n                share.warn(e);\n                this.onerror(e);\n            }\n        }\n        return data;\n    }\n    constructor(type){\n        super(), this.onerror = share.error;\n        this.type = type;\n    }\n}\n\nclass AsyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!share.isObject(data)) {\n            share.error(`The response data for the \"${this.type}\" hook must be an object.`);\n        }\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const processError = (e)=>{\n                share.warn(e);\n                this.onerror(e);\n                return data;\n            };\n            const call = (prevData)=>{\n                if (checkReturnData(data, prevData)) {\n                    data = prevData;\n                    if (i < ls.length) {\n                        try {\n                            return Promise.resolve(ls[i++](data)).then(call, processError);\n                        } catch (e) {\n                            return processError(e);\n                        }\n                    }\n                } else {\n                    this.onerror(`A plugin returned an incorrect value for the \"${this.type}\" type.`);\n                }\n                return data;\n            };\n            return Promise.resolve(call(data));\n        }\n        return Promise.resolve(data);\n    }\n    constructor(type){\n        super(), this.onerror = share.error;\n        this.type = type;\n    }\n}\n\nclass PluginSystem {\n    applyPlugin(plugin) {\n        share.assert(share.isPlainObject(plugin), 'Plugin configuration is invalid.');\n        // The plugin's name is mandatory and must be unique\n        const pluginName = plugin.name;\n        share.assert(pluginName, 'A name must be provided by the plugin.');\n        if (!this.registerPlugins[pluginName]) {\n            this.registerPlugins[pluginName] = plugin;\n            Object.keys(this.lifecycle).forEach((key)=>{\n                const pluginLife = plugin[key];\n                if (pluginLife) {\n                    this.lifecycle[key].on(pluginLife);\n                }\n            });\n        }\n    }\n    removePlugin(pluginName) {\n        share.assert(pluginName, 'A name is required.');\n        const plugin = this.registerPlugins[pluginName];\n        share.assert(plugin, `The plugin \"${pluginName}\" is not registered.`);\n        Object.keys(plugin).forEach((key)=>{\n            if (key !== 'name') {\n                this.lifecycle[key].remove(plugin[key]);\n            }\n        });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    inherit({ lifecycle, registerPlugins }) {\n        Object.keys(lifecycle).forEach((hookName)=>{\n            share.assert(!this.lifecycle[hookName], `The hook \"${hookName}\" has a conflict and cannot be inherited.`);\n            this.lifecycle[hookName] = lifecycle[hookName];\n        });\n        Object.keys(registerPlugins).forEach((pluginName)=>{\n            share.assert(!this.registerPlugins[pluginName], `The plugin \"${pluginName}\" has a conflict and cannot be inherited.`);\n            this.applyPlugin(registerPlugins[pluginName]);\n        });\n    }\n    constructor(lifecycle){\n        this.registerPlugins = {};\n        this.lifecycle = lifecycle;\n        this.lifecycleKeys = Object.keys(lifecycle);\n    }\n}\n\nfunction defaultPreloadArgs(preloadConfig) {\n    return polyfills._extends({\n        resourceCategory: 'sync',\n        share: true,\n        depsRemote: true,\n        prefetchInterface: false\n    }, preloadConfig);\n}\nfunction formatPreloadArgs(remotes, preloadArgs) {\n    return preloadArgs.map((args)=>{\n        const remoteInfo = matchRemote(remotes, args.nameOrAlias);\n        share.assert(remoteInfo, `Unable to preload ${args.nameOrAlias} as it is not included in ${!remoteInfo && sdk.safeToString({\n            remoteInfo,\n            remotes\n        })}`);\n        return {\n            remote: remoteInfo,\n            preloadConfig: defaultPreloadArgs(args)\n        };\n    });\n}\nfunction normalizePreloadExposes(exposes) {\n    if (!exposes) {\n        return [];\n    }\n    return exposes.map((expose)=>{\n        if (expose === '.') {\n            return expose;\n        }\n        if (expose.startsWith('./')) {\n            return expose.replace('./', '');\n        }\n        return expose;\n    });\n}\nfunction preloadAssets(remoteInfo, host, assets, // It is used to distinguish preload from load remote parallel loading\nuseLinkPreload = true) {\n    const { cssAssets, jsAssetsWithoutEntry, entryAssets } = assets;\n    if (host.options.inBrowser) {\n        entryAssets.forEach((asset)=>{\n            const { moduleInfo } = asset;\n            const module = host.moduleCache.get(remoteInfo.name);\n            if (module) {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: module.remoteEntryExports\n                });\n            } else {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: undefined\n                });\n            }\n        });\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'style'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        } else {\n            const defaultAttrs = {\n                rel: 'stylesheet',\n                type: 'text/css'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteLink: false\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        }\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'script'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { link: linkEl, needAttach } = sdk.createLink({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(linkEl);\n            });\n        } else {\n            const defaultAttrs = {\n                fetchpriority: 'high',\n                type: (remoteInfo == null ? void 0 : remoteInfo.type) === 'module' ? 'module' : 'text/javascript'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { script: scriptEl, needAttach } = sdk.createScript({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createScriptHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createScript.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLScriptElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteScript: true\n                });\n                needAttach && document.head.appendChild(scriptEl);\n            });\n        }\n    }\n}\n\nfunction assignRemoteInfo(remoteInfo, remoteSnapshot) {\n    const remoteEntryInfo = share.getRemoteEntryInfoFromSnapshot(remoteSnapshot);\n    if (!remoteEntryInfo.url) {\n        share.error(`The attribute remoteEntry of ${remoteInfo.name} must not be undefined.`);\n    }\n    let entryUrl = sdk.getResourceUrl(remoteSnapshot, remoteEntryInfo.url);\n    if (!sdk.isBrowserEnv() && !entryUrl.startsWith('http')) {\n        entryUrl = `https:${entryUrl}`;\n    }\n    remoteInfo.type = remoteEntryInfo.type;\n    remoteInfo.entryGlobalName = remoteEntryInfo.globalName;\n    remoteInfo.entry = entryUrl;\n    remoteInfo.version = remoteSnapshot.version;\n    remoteInfo.buildVersion = remoteSnapshot.buildVersion;\n}\nfunction snapshotPlugin() {\n    return {\n        name: 'snapshot-plugin',\n        async afterResolve (args) {\n            const { remote, pkgNameOrAlias, expose, origin, remoteInfo } = args;\n            if (!share.isRemoteInfoWithEntry(remote) || !share.isPureRemoteEntry(remote)) {\n                const { remoteSnapshot, globalSnapshot } = await origin.snapshotHandler.loadRemoteSnapshotInfo(remote);\n                assignRemoteInfo(remoteInfo, remoteSnapshot);\n                // preloading assets\n                const preloadOptions = {\n                    remote,\n                    preloadConfig: {\n                        nameOrAlias: pkgNameOrAlias,\n                        exposes: [\n                            expose\n                        ],\n                        resourceCategory: 'sync',\n                        share: false,\n                        depsRemote: false\n                    }\n                };\n                const assets = await origin.remoteHandler.hooks.lifecycle.generatePreloadAssets.emit({\n                    origin,\n                    preloadOptions,\n                    remoteInfo,\n                    remote,\n                    remoteSnapshot,\n                    globalSnapshot\n                });\n                if (assets) {\n                    preloadAssets(remoteInfo, origin, assets, false);\n                }\n                return polyfills._extends({}, args, {\n                    remoteSnapshot\n                });\n            }\n            return args;\n        }\n    };\n}\n\n// name\n// name:version\nfunction splitId(id) {\n    const splitInfo = id.split(':');\n    if (splitInfo.length === 1) {\n        return {\n            name: splitInfo[0],\n            version: undefined\n        };\n    } else if (splitInfo.length === 2) {\n        return {\n            name: splitInfo[0],\n            version: splitInfo[1]\n        };\n    } else {\n        return {\n            name: splitInfo[1],\n            version: splitInfo[2]\n        };\n    }\n}\n// Traverse all nodes in moduleInfo and traverse the entire snapshot\nfunction traverseModuleInfo(globalSnapshot, remoteInfo, traverse, isRoot, memo = {}, remoteSnapshot) {\n    const id = share.getFMId(remoteInfo);\n    const { value: snapshotValue } = share.getInfoWithoutType(globalSnapshot, id);\n    const effectiveRemoteSnapshot = remoteSnapshot || snapshotValue;\n    if (effectiveRemoteSnapshot && !sdk.isManifestProvider(effectiveRemoteSnapshot)) {\n        traverse(effectiveRemoteSnapshot, remoteInfo, isRoot);\n        if (effectiveRemoteSnapshot.remotesInfo) {\n            const remoteKeys = Object.keys(effectiveRemoteSnapshot.remotesInfo);\n            for (const key of remoteKeys){\n                if (memo[key]) {\n                    continue;\n                }\n                memo[key] = true;\n                const subRemoteInfo = splitId(key);\n                const remoteValue = effectiveRemoteSnapshot.remotesInfo[key];\n                traverseModuleInfo(globalSnapshot, {\n                    name: subRemoteInfo.name,\n                    version: remoteValue.matchedVersion\n                }, traverse, false, memo, undefined);\n            }\n        }\n    }\n}\n// eslint-disable-next-line max-lines-per-function\nfunction generatePreloadAssets(origin, preloadOptions, remote, globalSnapshot, remoteSnapshot) {\n    const cssAssets = [];\n    const jsAssets = [];\n    const entryAssets = [];\n    const loadedSharedJsAssets = new Set();\n    const loadedSharedCssAssets = new Set();\n    const { options } = origin;\n    const { preloadConfig: rootPreloadConfig } = preloadOptions;\n    const { depsRemote } = rootPreloadConfig;\n    const memo = {};\n    traverseModuleInfo(globalSnapshot, remote, (moduleInfoSnapshot, remoteInfo, isRoot)=>{\n        let preloadConfig;\n        if (isRoot) {\n            preloadConfig = rootPreloadConfig;\n        } else {\n            if (Array.isArray(depsRemote)) {\n                // eslint-disable-next-line array-callback-return\n                const findPreloadConfig = depsRemote.find((remoteConfig)=>{\n                    if (remoteConfig.nameOrAlias === remoteInfo.name || remoteConfig.nameOrAlias === remoteInfo.alias) {\n                        return true;\n                    }\n                    return false;\n                });\n                if (!findPreloadConfig) {\n                    return;\n                }\n                preloadConfig = defaultPreloadArgs(findPreloadConfig);\n            } else if (depsRemote === true) {\n                preloadConfig = rootPreloadConfig;\n            } else {\n                return;\n            }\n        }\n        const remoteEntryUrl = sdk.getResourceUrl(moduleInfoSnapshot, share.getRemoteEntryInfoFromSnapshot(moduleInfoSnapshot).url);\n        if (remoteEntryUrl) {\n            entryAssets.push({\n                name: remoteInfo.name,\n                moduleInfo: {\n                    name: remoteInfo.name,\n                    entry: remoteEntryUrl,\n                    type: 'remoteEntryType' in moduleInfoSnapshot ? moduleInfoSnapshot.remoteEntryType : 'global',\n                    entryGlobalName: 'globalName' in moduleInfoSnapshot ? moduleInfoSnapshot.globalName : remoteInfo.name,\n                    shareScope: '',\n                    version: 'version' in moduleInfoSnapshot ? moduleInfoSnapshot.version : undefined\n                },\n                url: remoteEntryUrl\n            });\n        }\n        let moduleAssetsInfo = 'modules' in moduleInfoSnapshot ? moduleInfoSnapshot.modules : [];\n        const normalizedPreloadExposes = normalizePreloadExposes(preloadConfig.exposes);\n        if (normalizedPreloadExposes.length && 'modules' in moduleInfoSnapshot) {\n            var _moduleInfoSnapshot_modules;\n            moduleAssetsInfo = moduleInfoSnapshot == null ? void 0 : (_moduleInfoSnapshot_modules = moduleInfoSnapshot.modules) == null ? void 0 : _moduleInfoSnapshot_modules.reduce((assets, moduleAssetInfo)=>{\n                if ((normalizedPreloadExposes == null ? void 0 : normalizedPreloadExposes.indexOf(moduleAssetInfo.moduleName)) !== -1) {\n                    assets.push(moduleAssetInfo);\n                }\n                return assets;\n            }, []);\n        }\n        function handleAssets(assets) {\n            const assetsRes = assets.map((asset)=>sdk.getResourceUrl(moduleInfoSnapshot, asset));\n            if (preloadConfig.filter) {\n                return assetsRes.filter(preloadConfig.filter);\n            }\n            return assetsRes;\n        }\n        if (moduleAssetsInfo) {\n            const assetsLength = moduleAssetsInfo.length;\n            for(let index = 0; index < assetsLength; index++){\n                const assetsInfo = moduleAssetsInfo[index];\n                const exposeFullPath = `${remoteInfo.name}/${assetsInfo.moduleName}`;\n                origin.remoteHandler.hooks.lifecycle.handlePreloadModule.emit({\n                    id: assetsInfo.moduleName === '.' ? remoteInfo.name : exposeFullPath,\n                    name: remoteInfo.name,\n                    remoteSnapshot: moduleInfoSnapshot,\n                    preloadConfig,\n                    remote: remoteInfo,\n                    origin\n                });\n                const preloaded = share.getPreloaded(exposeFullPath);\n                if (preloaded) {\n                    continue;\n                }\n                if (preloadConfig.resourceCategory === 'all') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.async));\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.async));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                // eslint-disable-next-line no-constant-condition\n                } else if (preloadConfig.resourceCategory = 'sync') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                }\n                share.setPreloaded(exposeFullPath);\n            }\n        }\n    }, true, memo, remoteSnapshot);\n    if (remoteSnapshot.shared) {\n        const collectSharedAssets = (shareInfo, snapshotShared)=>{\n            const registeredShared = share.getRegisteredShare(origin.shareScopeMap, snapshotShared.sharedName, shareInfo, origin.sharedHandler.hooks.lifecycle.resolveShare);\n            // If the global share does not exist, or the lib function does not exist, it means that the shared has not been loaded yet and can be preloaded.\n            if (registeredShared && typeof registeredShared.lib === 'function') {\n                snapshotShared.assets.js.sync.forEach((asset)=>{\n                    loadedSharedJsAssets.add(asset);\n                });\n                snapshotShared.assets.css.sync.forEach((asset)=>{\n                    loadedSharedCssAssets.add(asset);\n                });\n            }\n        };\n        remoteSnapshot.shared.forEach((shared)=>{\n            var _options_shared;\n            const shareInfos = (_options_shared = options.shared) == null ? void 0 : _options_shared[shared.sharedName];\n            if (!shareInfos) {\n                return;\n            }\n            // if no version, preload all shared\n            const sharedOptions = shared.version ? shareInfos.find((s)=>s.version === shared.version) : shareInfos;\n            if (!sharedOptions) {\n                return;\n            }\n            const arrayShareInfo = share.arrayOptions(sharedOptions);\n            arrayShareInfo.forEach((s)=>{\n                collectSharedAssets(s, shared);\n            });\n        });\n    }\n    const needPreloadJsAssets = jsAssets.filter((asset)=>!loadedSharedJsAssets.has(asset));\n    const needPreloadCssAssets = cssAssets.filter((asset)=>!loadedSharedCssAssets.has(asset));\n    return {\n        cssAssets: needPreloadCssAssets,\n        jsAssetsWithoutEntry: needPreloadJsAssets,\n        entryAssets\n    };\n}\nconst generatePreloadAssetsPlugin = function() {\n    return {\n        name: 'generate-preload-assets-plugin',\n        async generatePreloadAssets (args) {\n            const { origin, preloadOptions, remoteInfo, remote, globalSnapshot, remoteSnapshot } = args;\n            if (share.isRemoteInfoWithEntry(remote) && share.isPureRemoteEntry(remote)) {\n                return {\n                    cssAssets: [],\n                    jsAssetsWithoutEntry: [],\n                    entryAssets: [\n                        {\n                            name: remote.name,\n                            url: remote.entry,\n                            moduleInfo: {\n                                name: remoteInfo.name,\n                                entry: remote.entry,\n                                type: remoteInfo.type || 'global',\n                                entryGlobalName: '',\n                                shareScope: ''\n                            }\n                        }\n                    ]\n                };\n            }\n            assignRemoteInfo(remoteInfo, remoteSnapshot);\n            const assets = generatePreloadAssets(origin, preloadOptions, remoteInfo, globalSnapshot, remoteSnapshot);\n            return assets;\n        }\n    };\n};\n\nfunction getGlobalRemoteInfo(moduleInfo, origin) {\n    const hostGlobalSnapshot = share.getGlobalSnapshotInfoByModuleInfo({\n        name: origin.options.name,\n        version: origin.options.version\n    });\n    // get remote detail info from global\n    const globalRemoteInfo = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && share.getInfoWithoutType(hostGlobalSnapshot.remotesInfo, moduleInfo.name).value;\n    if (globalRemoteInfo && globalRemoteInfo.matchedVersion) {\n        return {\n            hostGlobalSnapshot,\n            globalSnapshot: share.getGlobalSnapshot(),\n            remoteSnapshot: share.getGlobalSnapshotInfoByModuleInfo({\n                name: moduleInfo.name,\n                version: globalRemoteInfo.matchedVersion\n            })\n        };\n    }\n    return {\n        hostGlobalSnapshot: undefined,\n        globalSnapshot: share.getGlobalSnapshot(),\n        remoteSnapshot: share.getGlobalSnapshotInfoByModuleInfo({\n            name: moduleInfo.name,\n            version: 'version' in moduleInfo ? moduleInfo.version : undefined\n        })\n    };\n}\nclass SnapshotHandler {\n    async loadSnapshot(moduleInfo) {\n        const { options } = this.HostInstance;\n        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);\n        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({\n            options,\n            moduleInfo,\n            hostGlobalSnapshot,\n            remoteSnapshot,\n            globalSnapshot\n        });\n        return {\n            remoteSnapshot: globalRemoteSnapshot,\n            globalSnapshot: globalSnapshotRes\n        };\n    }\n    // eslint-disable-next-line max-lines-per-function\n    async loadRemoteSnapshotInfo(moduleInfo) {\n        const { options } = this.HostInstance;\n        await this.hooks.lifecycle.beforeLoadRemoteSnapshot.emit({\n            options,\n            moduleInfo\n        });\n        let hostSnapshot = share.getGlobalSnapshotInfoByModuleInfo({\n            name: this.HostInstance.options.name,\n            version: this.HostInstance.options.version\n        });\n        if (!hostSnapshot) {\n            hostSnapshot = {\n                version: this.HostInstance.options.version || '',\n                remoteEntry: '',\n                remotesInfo: {}\n            };\n            share.addGlobalSnapshot({\n                [this.HostInstance.options.name]: hostSnapshot\n            });\n        }\n        // In dynamic loadRemote scenarios, incomplete remotesInfo delivery may occur. In such cases, the remotesInfo in the host needs to be completed in the snapshot at runtime.\n        // This ensures the snapshot's integrity and helps the chrome plugin correctly identify all producer modules, ensuring that proxyable producer modules will not be missing.\n        if (hostSnapshot && 'remotesInfo' in hostSnapshot && !share.getInfoWithoutType(hostSnapshot.remotesInfo, moduleInfo.name).value) {\n            if ('version' in moduleInfo || 'entry' in moduleInfo) {\n                hostSnapshot.remotesInfo = polyfills._extends({}, hostSnapshot == null ? void 0 : hostSnapshot.remotesInfo, {\n                    [moduleInfo.name]: {\n                        matchedVersion: 'version' in moduleInfo ? moduleInfo.version : moduleInfo.entry\n                    }\n                });\n            }\n        }\n        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);\n        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({\n            options,\n            moduleInfo,\n            hostGlobalSnapshot,\n            remoteSnapshot,\n            globalSnapshot\n        });\n        // global snapshot includes manifest or module info includes manifest\n        if (globalRemoteSnapshot) {\n            if (sdk.isManifestProvider(globalRemoteSnapshot)) {\n                const remoteEntry = sdk.isBrowserEnv() ? globalRemoteSnapshot.remoteEntry : globalRemoteSnapshot.ssrRemoteEntry || globalRemoteSnapshot.remoteEntry || '';\n                const moduleSnapshot = await this.getManifestJson(remoteEntry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = share.setGlobalSnapshotInfoByModuleInfo(polyfills._extends({}, moduleInfo, {\n                    // The global remote may be overridden\n                    // Therefore, set the snapshot key to the global address of the actual request\n                    entry: remoteEntry\n                }), moduleSnapshot);\n                return {\n                    remoteSnapshot: moduleSnapshot,\n                    globalSnapshot: globalSnapshotRes\n                };\n            } else {\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: globalRemoteSnapshot,\n                    from: 'global'\n                });\n                return {\n                    remoteSnapshot: remoteSnapshotRes,\n                    globalSnapshot: globalSnapshotRes\n                };\n            }\n        } else {\n            if (share.isRemoteInfoWithEntry(moduleInfo)) {\n                // get from manifest.json and merge remote info from remote server\n                const moduleSnapshot = await this.getManifestJson(moduleInfo.entry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = share.setGlobalSnapshotInfoByModuleInfo(moduleInfo, moduleSnapshot);\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: moduleSnapshot,\n                    from: 'global'\n                });\n                return {\n                    remoteSnapshot: remoteSnapshotRes,\n                    globalSnapshot: globalSnapshotRes\n                };\n            } else {\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_007, errorCodes.runtimeDescMap, {\n                    hostName: moduleInfo.name,\n                    hostVersion: moduleInfo.version,\n                    globalSnapshot: JSON.stringify(globalSnapshotRes)\n                }));\n            }\n        }\n    }\n    getGlobalRemoteInfo(moduleInfo) {\n        return getGlobalRemoteInfo(moduleInfo, this.HostInstance);\n    }\n    async getManifestJson(manifestUrl, moduleInfo, extraOptions) {\n        const getManifest = async ()=>{\n            let manifestJson = this.manifestCache.get(manifestUrl);\n            if (manifestJson) {\n                return manifestJson;\n            }\n            try {\n                let res = await this.loaderHook.lifecycle.fetch.emit(manifestUrl, {});\n                if (!res || !(res instanceof Response)) {\n                    res = await fetch(manifestUrl, {});\n                }\n                manifestJson = await res.json();\n                share.assert(manifestJson.metaData && manifestJson.exposes && manifestJson.shared, `${manifestUrl} is not a federation manifest`);\n                this.manifestCache.set(manifestUrl, manifestJson);\n                return manifestJson;\n            } catch (err) {\n                delete this.manifestLoading[manifestUrl];\n                share.error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_003, errorCodes.runtimeDescMap, {\n                    manifestUrl,\n                    moduleName: moduleInfo.name\n                }, `${err}`));\n            }\n        };\n        const asyncLoadProcess = async ()=>{\n            const manifestJson = await getManifest();\n            const remoteSnapshot = sdk.generateSnapshotFromManifest(manifestJson, {\n                version: manifestUrl\n            });\n            const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                options: this.HostInstance.options,\n                moduleInfo,\n                manifestJson,\n                remoteSnapshot,\n                manifestUrl,\n                from: 'manifest'\n            });\n            return remoteSnapshotRes;\n        };\n        if (!this.manifestLoading[manifestUrl]) {\n            this.manifestLoading[manifestUrl] = asyncLoadProcess().then((res)=>res);\n        }\n        return this.manifestLoading[manifestUrl];\n    }\n    constructor(HostInstance){\n        this.loadingHostSnapshot = null;\n        this.manifestCache = new Map();\n        this.hooks = new PluginSystem({\n            beforeLoadRemoteSnapshot: new AsyncHook('beforeLoadRemoteSnapshot'),\n            loadSnapshot: new AsyncWaterfallHook('loadGlobalSnapshot'),\n            loadRemoteSnapshot: new AsyncWaterfallHook('loadRemoteSnapshot')\n        });\n        this.manifestLoading = share.Global.__FEDERATION__.__MANIFEST_LOADING__;\n        this.HostInstance = HostInstance;\n        this.loaderHook = HostInstance.loaderHook;\n    }\n}\n\nclass SharedHandler {\n    // register shared in shareScopeMap\n    registerShared(globalOptions, userOptions) {\n        const { shareInfos, shared } = share.formatShareConfigs(globalOptions, userOptions);\n        const sharedKeys = Object.keys(shareInfos);\n        sharedKeys.forEach((sharedKey)=>{\n            const sharedVals = shareInfos[sharedKey];\n            sharedVals.forEach((sharedVal)=>{\n                const registeredShared = share.getRegisteredShare(this.shareScopeMap, sharedKey, sharedVal, this.hooks.lifecycle.resolveShare);\n                if (!registeredShared && sharedVal && sharedVal.lib) {\n                    this.setShared({\n                        pkgName: sharedKey,\n                        lib: sharedVal.lib,\n                        get: sharedVal.get,\n                        loaded: true,\n                        shared: sharedVal,\n                        from: userOptions.name\n                    });\n                }\n            });\n        });\n        return {\n            shareInfos,\n            shared\n        };\n    }\n    async loadShare(pkgName, extraOptions) {\n        const { host } = this;\n        // This function performs the following steps:\n        // 1. Checks if the currently loaded share already exists, if not, it throws an error\n        // 2. Searches globally for a matching share, if found, it uses it directly\n        // 3. If not found, it retrieves it from the current share and stores the obtained share globally.\n        const shareInfo = share.getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            await Promise.all(shareInfo.scope.map(async (shareScope)=>{\n                await Promise.all(this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                }));\n                return;\n            }));\n        }\n        const loadShareRes = await this.hooks.lifecycle.beforeLoadShare.emit({\n            pkgName,\n            shareInfo,\n            shared: host.options.shared,\n            origin: host\n        });\n        const { shareInfo: shareInfoRes } = loadShareRes;\n        // Assert that shareInfoRes exists, if not, throw an error\n        share.assert(shareInfoRes, `Cannot find ${pkgName} Share in the ${host.options.name}. Please ensure that the ${pkgName} Share parameters have been injected`);\n        // Retrieve from cache\n        const registeredShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            share.addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared && registeredShared.lib) {\n            addUseIn(registeredShared);\n            return registeredShared.lib;\n        } else if (registeredShared && registeredShared.loading && !registeredShared.loaded) {\n            const factory = await registeredShared.loading;\n            registeredShared.loaded = true;\n            if (!registeredShared.lib) {\n                registeredShared.lib = factory;\n            }\n            addUseIn(registeredShared);\n            return factory;\n        } else if (registeredShared) {\n            const asyncLoadProcess = async ()=>{\n                const factory = await registeredShared.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: registeredShared,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        } else {\n            if (extraOptions == null ? void 0 : extraOptions.customShareInfo) {\n                return false;\n            }\n            const asyncLoadProcess = async ()=>{\n                const factory = await shareInfoRes.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: shareInfoRes,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        }\n    }\n    /**\n   * This function initializes the sharing sequence (executed only once per share scope).\n   * It accepts one argument, the name of the share scope.\n   * If the share scope does not exist, it creates one.\n   */ // eslint-disable-next-line @typescript-eslint/member-ordering\n    initializeSharing(shareScopeName = share.DEFAULT_SCOPE, extraOptions) {\n        const { host } = this;\n        const from = extraOptions == null ? void 0 : extraOptions.from;\n        const strategy = extraOptions == null ? void 0 : extraOptions.strategy;\n        let initScope = extraOptions == null ? void 0 : extraOptions.initScope;\n        const promises = [];\n        if (from !== 'build') {\n            const { initTokens } = this;\n            if (!initScope) initScope = [];\n            let initToken = initTokens[shareScopeName];\n            if (!initToken) initToken = initTokens[shareScopeName] = {\n                from: this.host.name\n            };\n            if (initScope.indexOf(initToken) >= 0) return promises;\n            initScope.push(initToken);\n        }\n        const shareScope = this.shareScopeMap;\n        const hostName = host.options.name;\n        // Creates a new share scope if necessary\n        if (!shareScope[shareScopeName]) {\n            shareScope[shareScopeName] = {};\n        }\n        // Executes all initialization snippets from all accessible modules\n        const scope = shareScope[shareScopeName];\n        const register = (name, shared)=>{\n            var _activeVersion_shareConfig;\n            const { version, eager } = shared;\n            scope[name] = scope[name] || {};\n            const versions = scope[name];\n            const activeVersion = versions[version];\n            const activeVersionEager = Boolean(activeVersion && (activeVersion.eager || ((_activeVersion_shareConfig = activeVersion.shareConfig) == null ? void 0 : _activeVersion_shareConfig.eager)));\n            if (!activeVersion || activeVersion.strategy !== 'loaded-first' && !activeVersion.loaded && (Boolean(!eager) !== !activeVersionEager ? eager : hostName > activeVersion.from)) {\n                versions[version] = shared;\n            }\n        };\n        const initFn = (mod)=>mod && mod.init && mod.init(shareScope[shareScopeName], initScope);\n        const initRemoteModule = async (key)=>{\n            const { module } = await host.remoteHandler.getRemoteModuleAndOptions({\n                id: key\n            });\n            if (module.getEntry) {\n                let remoteEntryExports;\n                try {\n                    remoteEntryExports = await module.getEntry();\n                } catch (error) {\n                    remoteEntryExports = await host.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({\n                        id: key,\n                        error,\n                        from: 'runtime',\n                        lifecycle: 'beforeLoadShare',\n                        origin: host\n                    });\n                }\n                if (!module.inited) {\n                    await initFn(remoteEntryExports);\n                    module.inited = true;\n                }\n            }\n        };\n        Object.keys(host.options.shared).forEach((shareName)=>{\n            const sharedArr = host.options.shared[shareName];\n            sharedArr.forEach((shared)=>{\n                if (shared.scope.includes(shareScopeName)) {\n                    register(shareName, shared);\n                }\n            });\n        });\n        // TODO: strategy==='version-first' need to be removed in the future\n        if (host.options.shareStrategy === 'version-first' || strategy === 'version-first') {\n            host.options.remotes.forEach((remote)=>{\n                if (remote.shareScope === shareScopeName) {\n                    promises.push(initRemoteModule(remote.name));\n                }\n            });\n        }\n        return promises;\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        const { host } = this;\n        const shareInfo = share.getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            shareInfo.scope.forEach((shareScope)=>{\n                this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                });\n            });\n        }\n        const registeredShared = share.getRegisteredShare(this.shareScopeMap, pkgName, shareInfo, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            share.addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared) {\n            if (typeof registeredShared.lib === 'function') {\n                addUseIn(registeredShared);\n                if (!registeredShared.loaded) {\n                    registeredShared.loaded = true;\n                    if (registeredShared.from === host.options.name) {\n                        shareInfo.loaded = true;\n                    }\n                }\n                return registeredShared.lib;\n            }\n            if (typeof registeredShared.get === 'function') {\n                const module = registeredShared.get();\n                if (!(module instanceof Promise)) {\n                    addUseIn(registeredShared);\n                    this.setShared({\n                        pkgName,\n                        loaded: true,\n                        from: host.options.name,\n                        lib: module,\n                        shared: registeredShared\n                    });\n                    return module;\n                }\n            }\n        }\n        if (shareInfo.lib) {\n            if (!shareInfo.loaded) {\n                shareInfo.loaded = true;\n            }\n            return shareInfo.lib;\n        }\n        if (shareInfo.get) {\n            const module = shareInfo.get();\n            if (module instanceof Promise) {\n                const errorCode = (extraOptions == null ? void 0 : extraOptions.from) === 'build' ? errorCodes.RUNTIME_005 : errorCodes.RUNTIME_006;\n                throw new Error(errorCodes.getShortErrorMsg(errorCode, errorCodes.runtimeDescMap, {\n                    hostName: host.options.name,\n                    sharedPkgName: pkgName\n                }));\n            }\n            shareInfo.lib = module;\n            this.setShared({\n                pkgName,\n                loaded: true,\n                from: host.options.name,\n                lib: shareInfo.lib,\n                shared: shareInfo\n            });\n            return shareInfo.lib;\n        }\n        throw new Error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_006, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            sharedPkgName: pkgName\n        }));\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        const { host } = this;\n        this.shareScopeMap[scopeName] = shareScope;\n        this.hooks.lifecycle.initContainerShareScopeMap.emit({\n            shareScope,\n            options: host.options,\n            origin: host,\n            scopeName,\n            hostShareScopeMap: extraOptions.hostShareScopeMap\n        });\n    }\n    setShared({ pkgName, shared, from, lib, loading, loaded, get }) {\n        const { version, scope = 'default' } = shared, shareInfo = polyfills._object_without_properties_loose(shared, [\n            \"version\",\n            \"scope\"\n        ]);\n        const scopes = Array.isArray(scope) ? scope : [\n            scope\n        ];\n        scopes.forEach((sc)=>{\n            if (!this.shareScopeMap[sc]) {\n                this.shareScopeMap[sc] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName]) {\n                this.shareScopeMap[sc][pkgName] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName][version]) {\n                this.shareScopeMap[sc][pkgName][version] = polyfills._extends({\n                    version,\n                    scope: [\n                        'default'\n                    ]\n                }, shareInfo, {\n                    lib,\n                    loaded,\n                    loading\n                });\n                if (get) {\n                    this.shareScopeMap[sc][pkgName][version].get = get;\n                }\n                return;\n            }\n            const registeredShared = this.shareScopeMap[sc][pkgName][version];\n            if (loading && !registeredShared.loading) {\n                registeredShared.loading = loading;\n            }\n        });\n    }\n    _setGlobalShareScopeMap(hostOptions) {\n        const globalShareScopeMap = share.getGlobalShareScope();\n        const identifier = hostOptions.id || hostOptions.name;\n        if (identifier && !globalShareScopeMap[identifier]) {\n            globalShareScopeMap[identifier] = this.shareScopeMap;\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            afterResolve: new AsyncWaterfallHook('afterResolve'),\n            beforeLoadShare: new AsyncWaterfallHook('beforeLoadShare'),\n            // not used yet\n            loadShare: new AsyncHook(),\n            resolveShare: new SyncWaterfallHook('resolveShare'),\n            // maybe will change, temporarily for internal use only\n            initContainerShareScopeMap: new SyncWaterfallHook('initContainerShareScopeMap')\n        });\n        this.host = host;\n        this.shareScopeMap = {};\n        this.initTokens = {};\n        this._setGlobalShareScopeMap(host.options);\n    }\n}\n\nclass RemoteHandler {\n    formatAndRegisterRemote(globalOptions, userOptions) {\n        const userRemotes = userOptions.remotes || [];\n        return userRemotes.reduce((res, remote)=>{\n            this.registerRemote(remote, res, {\n                force: false\n            });\n            return res;\n        }, globalOptions.remotes);\n    }\n    setIdToRemoteMap(id, remoteMatchInfo) {\n        const { remote, expose } = remoteMatchInfo;\n        const { name, alias } = remote;\n        this.idToRemoteMap[id] = {\n            name: remote.name,\n            expose\n        };\n        if (alias && id.startsWith(name)) {\n            const idWithAlias = id.replace(name, alias);\n            this.idToRemoteMap[idWithAlias] = {\n                name: remote.name,\n                expose\n            };\n            return;\n        }\n        if (alias && id.startsWith(alias)) {\n            const idWithName = id.replace(alias, name);\n            this.idToRemoteMap[idWithName] = {\n                name: remote.name,\n                expose\n            };\n        }\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        const { host } = this;\n        try {\n            const { loadFactory = true } = options || {\n                loadFactory: true\n            };\n            // 1. Validate the parameters of the retrieved module. There are two module request methods: pkgName + expose and alias + expose.\n            // 2. Request the snapshot information of the current host and globally store the obtained snapshot information. The retrieved module information is partially offline and partially online. The online module information will retrieve the modules used online.\n            // 3. Retrieve the detailed information of the current module from global (remoteEntry address, expose resource address)\n            // 4. After retrieving remoteEntry, call the init of the module, and then retrieve the exported content of the module through get\n            // id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n            // id: alias(app1) + expose(button) = app1/button\n            // id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\n            const { module, moduleOptions, remoteMatchInfo } = await this.getRemoteModuleAndOptions({\n                id\n            });\n            const { pkgNameOrAlias, remote, expose, id: idRes, remoteSnapshot } = remoteMatchInfo;\n            const moduleOrFactory = await module.get(idRes, expose, options, remoteSnapshot);\n            const moduleWrapper = await this.hooks.lifecycle.onLoad.emit({\n                id: idRes,\n                pkgNameOrAlias,\n                expose,\n                exposeModule: loadFactory ? moduleOrFactory : undefined,\n                exposeModuleFactory: loadFactory ? undefined : moduleOrFactory,\n                remote,\n                options: moduleOptions,\n                moduleInstance: module,\n                origin: host\n            });\n            this.setIdToRemoteMap(id, remoteMatchInfo);\n            if (typeof moduleWrapper === 'function') {\n                return moduleWrapper;\n            }\n            return moduleOrFactory;\n        } catch (error) {\n            const { from = 'runtime' } = options || {\n                from: 'runtime'\n            };\n            const failOver = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                error,\n                from,\n                lifecycle: 'onLoad',\n                origin: host\n            });\n            if (!failOver) {\n                throw error;\n            }\n            return failOver;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        const { host } = this;\n        await this.hooks.lifecycle.beforePreloadRemote.emit({\n            preloadOps: preloadOptions,\n            options: host.options,\n            origin: host\n        });\n        const preloadOps = formatPreloadArgs(host.options.remotes, preloadOptions);\n        await Promise.all(preloadOps.map(async (ops)=>{\n            const { remote } = ops;\n            const remoteInfo = getRemoteInfo(remote);\n            const { globalSnapshot, remoteSnapshot } = await host.snapshotHandler.loadRemoteSnapshotInfo(remote);\n            const assets = await this.hooks.lifecycle.generatePreloadAssets.emit({\n                origin: host,\n                preloadOptions: ops,\n                remote,\n                remoteInfo,\n                globalSnapshot,\n                remoteSnapshot\n            });\n            if (!assets) {\n                return;\n            }\n            preloadAssets(remoteInfo, host, assets);\n        }));\n    }\n    registerRemotes(remotes, options) {\n        const { host } = this;\n        remotes.forEach((remote)=>{\n            this.registerRemote(remote, host.options.remotes, {\n                force: options == null ? void 0 : options.force\n            });\n        });\n    }\n    async getRemoteModuleAndOptions(options) {\n        const { host } = this;\n        const { id } = options;\n        let loadRemoteArgs;\n        try {\n            loadRemoteArgs = await this.hooks.lifecycle.beforeRequest.emit({\n                id,\n                options: host.options,\n                origin: host\n            });\n        } catch (error) {\n            loadRemoteArgs = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                options: host.options,\n                origin: host,\n                from: 'runtime',\n                error,\n                lifecycle: 'beforeRequest'\n            });\n            if (!loadRemoteArgs) {\n                throw error;\n            }\n        }\n        const { id: idRes } = loadRemoteArgs;\n        const remoteSplitInfo = matchRemoteWithNameAndExpose(host.options.remotes, idRes);\n        share.assert(remoteSplitInfo, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_004, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            requestId: idRes\n        }));\n        const { remote: rawRemote } = remoteSplitInfo;\n        const remoteInfo = getRemoteInfo(rawRemote);\n        const matchInfo = await host.sharedHandler.hooks.lifecycle.afterResolve.emit(polyfills._extends({\n            id: idRes\n        }, remoteSplitInfo, {\n            options: host.options,\n            origin: host,\n            remoteInfo\n        }));\n        const { remote, expose } = matchInfo;\n        share.assert(remote && expose, `The 'beforeRequest' hook was executed, but it failed to return the correct 'remote' and 'expose' values while loading ${idRes}.`);\n        let module = host.moduleCache.get(remote.name);\n        const moduleOptions = {\n            host: host,\n            remoteInfo\n        };\n        if (!module) {\n            module = new Module(moduleOptions);\n            host.moduleCache.set(remote.name, module);\n        }\n        return {\n            module,\n            moduleOptions,\n            remoteMatchInfo: matchInfo\n        };\n    }\n    registerRemote(remote, targetRemotes, options) {\n        const { host } = this;\n        const normalizeRemote = ()=>{\n            if (remote.alias) {\n                // Validate if alias equals the prefix of remote.name and remote.alias, if so, throw an error\n                // As multi-level path references cannot guarantee unique names, alias being a prefix of remote.name is not supported\n                const findEqual = targetRemotes.find((item)=>{\n                    var _item_alias;\n                    return remote.alias && (item.name.startsWith(remote.alias) || ((_item_alias = item.alias) == null ? void 0 : _item_alias.startsWith(remote.alias)));\n                });\n                share.assert(!findEqual, `The alias ${remote.alias} of remote ${remote.name} is not allowed to be the prefix of ${findEqual && findEqual.name} name or alias`);\n            }\n            // Set the remote entry to a complete path\n            if ('entry' in remote) {\n                if (sdk.isBrowserEnv() && !remote.entry.startsWith('http')) {\n                    remote.entry = new URL(remote.entry, window.location.origin).href;\n                }\n            }\n            if (!remote.shareScope) {\n                remote.shareScope = share.DEFAULT_SCOPE;\n            }\n            if (!remote.type) {\n                remote.type = share.DEFAULT_REMOTE_TYPE;\n            }\n        };\n        this.hooks.lifecycle.beforeRegisterRemote.emit({\n            remote,\n            origin: host\n        });\n        const registeredRemote = targetRemotes.find((item)=>item.name === remote.name);\n        if (!registeredRemote) {\n            normalizeRemote();\n            targetRemotes.push(remote);\n            this.hooks.lifecycle.registerRemote.emit({\n                remote,\n                origin: host\n            });\n        } else {\n            const messages = [\n                `The remote \"${remote.name}\" is already registered.`,\n                (options == null ? void 0 : options.force) ? 'Hope you have known that OVERRIDE it may have some unexpected errors' : 'If you want to merge the remote, you can set \"force: true\".'\n            ];\n            if (options == null ? void 0 : options.force) {\n                // remove registered remote\n                this.removeRemote(registeredRemote);\n                normalizeRemote();\n                targetRemotes.push(remote);\n                this.hooks.lifecycle.registerRemote.emit({\n                    remote,\n                    origin: host\n                });\n            }\n            sdk.warn(messages.join(' '));\n        }\n    }\n    removeRemote(remote) {\n        try {\n            const { host } = this;\n            const { name } = remote;\n            const remoteIndex = host.options.remotes.findIndex((item)=>item.name === name);\n            if (remoteIndex !== -1) {\n                host.options.remotes.splice(remoteIndex, 1);\n            }\n            const loadedModule = host.moduleCache.get(remote.name);\n            if (loadedModule) {\n                const remoteInfo = loadedModule.remoteInfo;\n                const key = remoteInfo.entryGlobalName;\n                if (globalThis[key]) {\n                    var _Object_getOwnPropertyDescriptor;\n                    if ((_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(globalThis, key)) == null ? void 0 : _Object_getOwnPropertyDescriptor.configurable) {\n                        delete globalThis[key];\n                    } else {\n                        // @ts-ignore\n                        globalThis[key] = undefined;\n                    }\n                }\n                const remoteEntryUniqueKey = getRemoteEntryUniqueKey(loadedModule.remoteInfo);\n                if (share.globalLoading[remoteEntryUniqueKey]) {\n                    delete share.globalLoading[remoteEntryUniqueKey];\n                }\n                host.snapshotHandler.manifestCache.delete(remoteInfo.entry);\n                // delete unloaded shared and instance\n                let remoteInsId = remoteInfo.buildVersion ? sdk.composeKeyWithSeparator(remoteInfo.name, remoteInfo.buildVersion) : remoteInfo.name;\n                const remoteInsIndex = globalThis.__FEDERATION__.__INSTANCES__.findIndex((ins)=>{\n                    if (remoteInfo.buildVersion) {\n                        return ins.options.id === remoteInsId;\n                    } else {\n                        return ins.name === remoteInsId;\n                    }\n                });\n                if (remoteInsIndex !== -1) {\n                    const remoteIns = globalThis.__FEDERATION__.__INSTANCES__[remoteInsIndex];\n                    remoteInsId = remoteIns.options.id || remoteInsId;\n                    const globalShareScopeMap = share.getGlobalShareScope();\n                    let isAllSharedNotUsed = true;\n                    const needDeleteKeys = [];\n                    Object.keys(globalShareScopeMap).forEach((instId)=>{\n                        const shareScopeMap = globalShareScopeMap[instId];\n                        shareScopeMap && Object.keys(shareScopeMap).forEach((shareScope)=>{\n                            const shareScopeVal = shareScopeMap[shareScope];\n                            shareScopeVal && Object.keys(shareScopeVal).forEach((shareName)=>{\n                                const sharedPkgs = shareScopeVal[shareName];\n                                sharedPkgs && Object.keys(sharedPkgs).forEach((shareVersion)=>{\n                                    const shared = sharedPkgs[shareVersion];\n                                    if (shared && typeof shared === 'object' && shared.from === remoteInfo.name) {\n                                        if (shared.loaded || shared.loading) {\n                                            shared.useIn = shared.useIn.filter((usedHostName)=>usedHostName !== remoteInfo.name);\n                                            if (shared.useIn.length) {\n                                                isAllSharedNotUsed = false;\n                                            } else {\n                                                needDeleteKeys.push([\n                                                    instId,\n                                                    shareScope,\n                                                    shareName,\n                                                    shareVersion\n                                                ]);\n                                            }\n                                        } else {\n                                            needDeleteKeys.push([\n                                                instId,\n                                                shareScope,\n                                                shareName,\n                                                shareVersion\n                                            ]);\n                                        }\n                                    }\n                                });\n                            });\n                        });\n                    });\n                    if (isAllSharedNotUsed) {\n                        remoteIns.shareScopeMap = {};\n                        delete globalShareScopeMap[remoteInsId];\n                    }\n                    needDeleteKeys.forEach(([insId, shareScope, shareName, shareVersion])=>{\n                        var _globalShareScopeMap_insId_shareScope_shareName, _globalShareScopeMap_insId_shareScope, _globalShareScopeMap_insId;\n                        (_globalShareScopeMap_insId = globalShareScopeMap[insId]) == null ? true : (_globalShareScopeMap_insId_shareScope = _globalShareScopeMap_insId[shareScope]) == null ? true : (_globalShareScopeMap_insId_shareScope_shareName = _globalShareScopeMap_insId_shareScope[shareName]) == null ? true : delete _globalShareScopeMap_insId_shareScope_shareName[shareVersion];\n                    });\n                    globalThis.__FEDERATION__.__INSTANCES__.splice(remoteInsIndex, 1);\n                }\n                const { hostGlobalSnapshot } = getGlobalRemoteInfo(remote, host);\n                if (hostGlobalSnapshot) {\n                    const remoteKey = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && share.getInfoWithoutType(hostGlobalSnapshot.remotesInfo, remote.name).key;\n                    if (remoteKey) {\n                        delete hostGlobalSnapshot.remotesInfo[remoteKey];\n                        if (//eslint-disable-next-line no-extra-boolean-cast\n                        Boolean(share.Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey])) {\n                            delete share.Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey];\n                        }\n                    }\n                }\n                host.moduleCache.delete(remote.name);\n            }\n        } catch (err) {\n            share.logger.log('removeRemote fail: ', err);\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            beforeRegisterRemote: new SyncWaterfallHook('beforeRegisterRemote'),\n            registerRemote: new SyncWaterfallHook('registerRemote'),\n            beforeRequest: new AsyncWaterfallHook('beforeRequest'),\n            onLoad: new AsyncHook('onLoad'),\n            handlePreloadModule: new SyncHook('handlePreloadModule'),\n            errorLoadRemote: new AsyncHook('errorLoadRemote'),\n            beforePreloadRemote: new AsyncHook('beforePreloadRemote'),\n            generatePreloadAssets: new AsyncHook('generatePreloadAssets'),\n            // not used yet\n            afterPreloadRemote: new AsyncHook(),\n            loadEntry: new AsyncHook()\n        });\n        this.host = host;\n        this.idToRemoteMap = {};\n    }\n}\n\nclass FederationHost {\n    initOptions(userOptions) {\n        this.registerPlugins(userOptions.plugins);\n        const options = this.formatOptions(this.options, userOptions);\n        this.options = options;\n        return options;\n    }\n    async loadShare(pkgName, extraOptions) {\n        return this.sharedHandler.loadShare(pkgName, extraOptions);\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        return this.sharedHandler.loadShareSync(pkgName, extraOptions);\n    }\n    initializeSharing(shareScopeName = share.DEFAULT_SCOPE, extraOptions) {\n        return this.sharedHandler.initializeSharing(shareScopeName, extraOptions);\n    }\n    initRawContainer(name, url, container) {\n        const remoteInfo = getRemoteInfo({\n            name,\n            entry: url\n        });\n        const module = new Module({\n            host: this,\n            remoteInfo\n        });\n        module.remoteEntryExports = container;\n        this.moduleCache.set(name, module);\n        return module;\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        return this.remoteHandler.loadRemote(id, options);\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        return this.remoteHandler.preloadRemote(preloadOptions);\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        this.sharedHandler.initShareScopeMap(scopeName, shareScope, extraOptions);\n    }\n    formatOptions(globalOptions, userOptions) {\n        const { shared } = share.formatShareConfigs(globalOptions, userOptions);\n        const { userOptions: userOptionsRes, options: globalOptionsRes } = this.hooks.lifecycle.beforeInit.emit({\n            origin: this,\n            userOptions,\n            options: globalOptions,\n            shareInfo: shared\n        });\n        const remotes = this.remoteHandler.formatAndRegisterRemote(globalOptionsRes, userOptionsRes);\n        const { shared: handledShared } = this.sharedHandler.registerShared(globalOptionsRes, userOptionsRes);\n        const plugins = [\n            ...globalOptionsRes.plugins\n        ];\n        if (userOptionsRes.plugins) {\n            userOptionsRes.plugins.forEach((plugin)=>{\n                if (!plugins.includes(plugin)) {\n                    plugins.push(plugin);\n                }\n            });\n        }\n        const optionsRes = polyfills._extends({}, globalOptions, userOptions, {\n            plugins,\n            remotes,\n            shared: handledShared\n        });\n        this.hooks.lifecycle.init.emit({\n            origin: this,\n            options: optionsRes\n        });\n        return optionsRes;\n    }\n    registerPlugins(plugins) {\n        const pluginRes = registerPlugins$1(plugins, [\n            this.hooks,\n            this.remoteHandler.hooks,\n            this.sharedHandler.hooks,\n            this.snapshotHandler.hooks,\n            this.loaderHook\n        ]);\n        // Merge plugin\n        this.options.plugins = this.options.plugins.reduce((res, plugin)=>{\n            if (!plugin) return res;\n            if (res && !res.find((item)=>item.name === plugin.name)) {\n                res.push(plugin);\n            }\n            return res;\n        }, pluginRes || []);\n    }\n    registerRemotes(remotes, options) {\n        return this.remoteHandler.registerRemotes(remotes, options);\n    }\n    constructor(userOptions){\n        this.hooks = new PluginSystem({\n            beforeInit: new SyncWaterfallHook('beforeInit'),\n            init: new SyncHook(),\n            // maybe will change, temporarily for internal use only\n            beforeInitContainer: new AsyncWaterfallHook('beforeInitContainer'),\n            // maybe will change, temporarily for internal use only\n            initContainer: new AsyncWaterfallHook('initContainer')\n        });\n        this.version = \"0.6.16\";\n        this.moduleCache = new Map();\n        this.loaderHook = new PluginSystem({\n            // FIXME: may not be suitable , not open to the public yet\n            getModuleInfo: new SyncHook(),\n            createScript: new SyncHook(),\n            createLink: new SyncHook(),\n            // only work for manifest , so not open to the public yet\n            fetch: new AsyncHook(),\n            getModuleFactory: new AsyncHook()\n        });\n        // TODO: Validate the details of the options\n        // Initialize options with default values\n        const defaultOptions = {\n            id: share.getBuilderId(),\n            name: userOptions.name,\n            plugins: [\n                snapshotPlugin(),\n                generatePreloadAssetsPlugin()\n            ],\n            remotes: [],\n            shared: {},\n            inBrowser: sdk.isBrowserEnv()\n        };\n        this.name = userOptions.name;\n        this.options = defaultOptions;\n        this.snapshotHandler = new SnapshotHandler(this);\n        this.sharedHandler = new SharedHandler(this);\n        this.remoteHandler = new RemoteHandler(this);\n        this.shareScopeMap = this.sharedHandler.shareScopeMap;\n        this.registerPlugins([\n            ...defaultOptions.plugins,\n            ...userOptions.plugins || []\n        ]);\n        this.options = this.formatOptions(defaultOptions, userOptions);\n    }\n}\n\nlet FederationInstance = null;\nfunction init(options) {\n    // Retrieve the same instance with the same name\n    const instance = share.getGlobalFederationInstance(options.name, options.version);\n    if (!instance) {\n        // Retrieve debug constructor\n        const FederationConstructor = share.getGlobalFederationConstructor() || FederationHost;\n        FederationInstance = new FederationConstructor(options);\n        share.setGlobalFederationInstance(FederationInstance);\n        return FederationInstance;\n    } else {\n        // Merge options\n        instance.initOptions(options);\n        if (!FederationInstance) {\n            FederationInstance = instance;\n        }\n        return instance;\n    }\n}\nfunction loadRemote(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    const loadRemote1 = FederationInstance.loadRemote;\n    // eslint-disable-next-line prefer-spread\n    return loadRemote1.apply(FederationInstance, args);\n}\nfunction loadShare(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    const loadShare1 = FederationInstance.loadShare;\n    return loadShare1.apply(FederationInstance, args);\n}\nfunction loadShareSync(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    const loadShareSync1 = FederationInstance.loadShareSync;\n    // eslint-disable-next-line prefer-spread\n    return loadShareSync1.apply(FederationInstance, args);\n}\nfunction preloadRemote(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.preloadRemote.apply(FederationInstance, args);\n}\nfunction registerRemotes(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerRemotes.apply(FederationInstance, args);\n}\nfunction registerPlugins(...args) {\n    share.assert(FederationInstance, 'Please call init first');\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerPlugins.apply(FederationInstance, args);\n}\nfunction getInstance() {\n    return FederationInstance;\n}\n// Inject for debug\nshare.setGlobalFederationConstructor(FederationHost);\n\nObject.defineProperty(exports, \"loadScript\", ({\n  enumerable: true,\n  get: function () { return sdk.loadScript; }\n}));\nObject.defineProperty(exports, \"loadScriptNode\", ({\n  enumerable: true,\n  get: function () { return sdk.loadScriptNode; }\n}));\nexports.registerGlobalPlugins = share.registerGlobalPlugins;\nexports.FederationHost = FederationHost;\nexports.Module = Module;\nexports.getInstance = getInstance;\nexports.getRemoteEntry = getRemoteEntry;\nexports.getRemoteInfo = getRemoteInfo;\nexports.init = init;\nexports.loadRemote = loadRemote;\nexports.loadShare = loadShare;\nexports.loadShareSync = loadShareSync;\nexports.preloadRemote = preloadRemote;\nexports.registerPlugins = registerPlugins;\nexports.registerRemotes = registerRemotes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3197\n\n}");

/***/ }),

/***/ 3767:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nconst RUNTIME_001 = 'RUNTIME-001';\nconst RUNTIME_002 = 'RUNTIME-002';\nconst RUNTIME_003 = 'RUNTIME-003';\nconst RUNTIME_004 = 'RUNTIME-004';\nconst RUNTIME_005 = 'RUNTIME-005';\nconst RUNTIME_006 = 'RUNTIME-006';\nconst RUNTIME_007 = 'RUNTIME-007';\nconst TYPE_001 = 'TYPE-001';\n\nconst getDocsUrl = (errorCode)=>{\n    const type = errorCode.split('-')[0].toLowerCase();\n    return `https://module-federation.io/guide/troubleshooting/${type}/${errorCode}`;\n};\nconst getShortErrorMsg = (errorCode, errorDescMap, args, originalErrorMsg)=>{\n    const msg = [\n        errorDescMap[errorCode]\n    ];\n    args && msg.push(`args: ${JSON.stringify(args)}`);\n    msg.push(getDocsUrl(errorCode));\n    originalErrorMsg && msg.push(`Original Error Message:\\n ${originalErrorMsg}`);\n    return msg.join('\\n');\n};\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nconst runtimeDescMap = {\n    [RUNTIME_001]: 'Failed to get remoteEntry exports.',\n    [RUNTIME_002]: 'The remote entry interface does not contain \"init\"',\n    [RUNTIME_003]: 'Failed to get manifest.',\n    [RUNTIME_004]: 'Failed to locate remote.',\n    [RUNTIME_005]: 'Invalid loadShareSync function call from bundler runtime',\n    [RUNTIME_006]: 'Invalid loadShareSync function call from runtime',\n    [RUNTIME_007]: 'Failed to get remote snapshot.'\n};\nconst typeDescMap = {\n    [TYPE_001]: 'Failed to generate type declaration.'\n};\nconst errorDescMap = _extends({}, runtimeDescMap, typeDescMap);\n\nexports.RUNTIME_001 = RUNTIME_001;\nexports.RUNTIME_002 = RUNTIME_002;\nexports.RUNTIME_003 = RUNTIME_003;\nexports.RUNTIME_004 = RUNTIME_004;\nexports.RUNTIME_005 = RUNTIME_005;\nexports.RUNTIME_006 = RUNTIME_006;\nexports.RUNTIME_007 = RUNTIME_007;\nexports.TYPE_001 = TYPE_001;\nexports.errorDescMap = errorDescMap;\nexports.getShortErrorMsg = getShortErrorMsg;\nexports.runtimeDescMap = runtimeDescMap;\nexports.typeDescMap = typeDescMap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzc2Ny5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpRUFBaUUsS0FBSyxHQUFHLFVBQVU7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixxQkFBcUI7QUFDbkQ7QUFDQSw4REFBOEQsaUJBQWlCO0FBQy9FO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHVCQUF1QixzQkFBc0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDOztBQUVoQyxtQkFBbUI7QUFDbkIsbUJBQW1CO0FBQ25CLG1CQUFtQjtBQUNuQixtQkFBbUI7QUFDbkIsbUJBQW1CO0FBQ25CLG1CQUFtQjtBQUNuQixtQkFBbUI7QUFDbkIsZ0JBQWdCO0FBQ2hCLG9CQUFvQjtBQUNwQix3QkFBd0I7QUFDeEIsc0JBQXNCO0FBQ3RCLG1CQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL0Bjcm0vaWYtcGFydHktbWFzdGVyLy4uLy4uL25vZGVfbW9kdWxlcy9AbW9kdWxlLWZlZGVyYXRpb24vZXJyb3ItY29kZXMvZGlzdC9pbmRleC5janMuanM/NzVlMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IFJVTlRJTUVfMDAxID0gJ1JVTlRJTUUtMDAxJztcbmNvbnN0IFJVTlRJTUVfMDAyID0gJ1JVTlRJTUUtMDAyJztcbmNvbnN0IFJVTlRJTUVfMDAzID0gJ1JVTlRJTUUtMDAzJztcbmNvbnN0IFJVTlRJTUVfMDA0ID0gJ1JVTlRJTUUtMDA0JztcbmNvbnN0IFJVTlRJTUVfMDA1ID0gJ1JVTlRJTUUtMDA1JztcbmNvbnN0IFJVTlRJTUVfMDA2ID0gJ1JVTlRJTUUtMDA2JztcbmNvbnN0IFJVTlRJTUVfMDA3ID0gJ1JVTlRJTUUtMDA3JztcbmNvbnN0IFRZUEVfMDAxID0gJ1RZUEUtMDAxJztcblxuY29uc3QgZ2V0RG9jc1VybCA9IChlcnJvckNvZGUpPT57XG4gICAgY29uc3QgdHlwZSA9IGVycm9yQ29kZS5zcGxpdCgnLScpWzBdLnRvTG93ZXJDYXNlKCk7XG4gICAgcmV0dXJuIGBodHRwczovL21vZHVsZS1mZWRlcmF0aW9uLmlvL2d1aWRlL3Ryb3VibGVzaG9vdGluZy8ke3R5cGV9LyR7ZXJyb3JDb2RlfWA7XG59O1xuY29uc3QgZ2V0U2hvcnRFcnJvck1zZyA9IChlcnJvckNvZGUsIGVycm9yRGVzY01hcCwgYXJncywgb3JpZ2luYWxFcnJvck1zZyk9PntcbiAgICBjb25zdCBtc2cgPSBbXG4gICAgICAgIGVycm9yRGVzY01hcFtlcnJvckNvZGVdXG4gICAgXTtcbiAgICBhcmdzICYmIG1zZy5wdXNoKGBhcmdzOiAke0pTT04uc3RyaW5naWZ5KGFyZ3MpfWApO1xuICAgIG1zZy5wdXNoKGdldERvY3NVcmwoZXJyb3JDb2RlKSk7XG4gICAgb3JpZ2luYWxFcnJvck1zZyAmJiBtc2cucHVzaChgT3JpZ2luYWwgRXJyb3IgTWVzc2FnZTpcXG4gJHtvcmlnaW5hbEVycm9yTXNnfWApO1xuICAgIHJldHVybiBtc2cuam9pbignXFxuJyk7XG59O1xuXG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgICBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gYXNzaWduKHRhcmdldCkge1xuICAgICAgICBmb3IodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAgICAgIHZhciBzb3VyY2UgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IodmFyIGtleSBpbiBzb3VyY2UpaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzb3VyY2UsIGtleSkpIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRhcmdldDtcbiAgICB9O1xuICAgIHJldHVybiBfZXh0ZW5kcy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuXG5jb25zdCBydW50aW1lRGVzY01hcCA9IHtcbiAgICBbUlVOVElNRV8wMDFdOiAnRmFpbGVkIHRvIGdldCByZW1vdGVFbnRyeSBleHBvcnRzLicsXG4gICAgW1JVTlRJTUVfMDAyXTogJ1RoZSByZW1vdGUgZW50cnkgaW50ZXJmYWNlIGRvZXMgbm90IGNvbnRhaW4gXCJpbml0XCInLFxuICAgIFtSVU5USU1FXzAwM106ICdGYWlsZWQgdG8gZ2V0IG1hbmlmZXN0LicsXG4gICAgW1JVTlRJTUVfMDA0XTogJ0ZhaWxlZCB0byBsb2NhdGUgcmVtb3RlLicsXG4gICAgW1JVTlRJTUVfMDA1XTogJ0ludmFsaWQgbG9hZFNoYXJlU3luYyBmdW5jdGlvbiBjYWxsIGZyb20gYnVuZGxlciBydW50aW1lJyxcbiAgICBbUlVOVElNRV8wMDZdOiAnSW52YWxpZCBsb2FkU2hhcmVTeW5jIGZ1bmN0aW9uIGNhbGwgZnJvbSBydW50aW1lJyxcbiAgICBbUlVOVElNRV8wMDddOiAnRmFpbGVkIHRvIGdldCByZW1vdGUgc25hcHNob3QuJ1xufTtcbmNvbnN0IHR5cGVEZXNjTWFwID0ge1xuICAgIFtUWVBFXzAwMV06ICdGYWlsZWQgdG8gZ2VuZXJhdGUgdHlwZSBkZWNsYXJhdGlvbi4nXG59O1xuY29uc3QgZXJyb3JEZXNjTWFwID0gX2V4dGVuZHMoe30sIHJ1bnRpbWVEZXNjTWFwLCB0eXBlRGVzY01hcCk7XG5cbmV4cG9ydHMuUlVOVElNRV8wMDEgPSBSVU5USU1FXzAwMTtcbmV4cG9ydHMuUlVOVElNRV8wMDIgPSBSVU5USU1FXzAwMjtcbmV4cG9ydHMuUlVOVElNRV8wMDMgPSBSVU5USU1FXzAwMztcbmV4cG9ydHMuUlVOVElNRV8wMDQgPSBSVU5USU1FXzAwNDtcbmV4cG9ydHMuUlVOVElNRV8wMDUgPSBSVU5USU1FXzAwNTtcbmV4cG9ydHMuUlVOVElNRV8wMDYgPSBSVU5USU1FXzAwNjtcbmV4cG9ydHMuUlVOVElNRV8wMDcgPSBSVU5USU1FXzAwNztcbmV4cG9ydHMuVFlQRV8wMDEgPSBUWVBFXzAwMTtcbmV4cG9ydHMuZXJyb3JEZXNjTWFwID0gZXJyb3JEZXNjTWFwO1xuZXhwb3J0cy5nZXRTaG9ydEVycm9yTXNnID0gZ2V0U2hvcnRFcnJvck1zZztcbmV4cG9ydHMucnVudGltZURlc2NNYXAgPSBydW50aW1lRGVzY01hcDtcbmV4cG9ydHMudHlwZURlc2NNYXAgPSB0eXBlRGVzY01hcDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///3767\n\n}");

/***/ }),

/***/ 4179:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar sdk = __webpack_require__(9468);\n\nconst FEDERATION_SUPPORTED_TYPES = [\n    'script'\n];\n\nObject.defineProperty(exports, \"ENCODE_NAME_PREFIX\", ({\n\tenumerable: true,\n\tget: function () { return sdk.ENCODE_NAME_PREFIX; }\n}));\nexports.FEDERATION_SUPPORTED_TYPES = FEDERATION_SUPPORTED_TYPES;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDE3OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixVQUFVLG1CQUFPLENBQUMsSUFBd0I7O0FBRTFDO0FBQ0E7QUFDQTs7QUFFQSxzREFBcUQ7QUFDckQ7QUFDQSxvQkFBb0I7QUFDcEIsQ0FBQyxFQUFDO0FBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGNybS9pZi1wYXJ0eS1tYXN0ZXIvLi4vLi4vbm9kZV9tb2R1bGVzL0Btb2R1bGUtZmVkZXJhdGlvbi93ZWJwYWNrLWJ1bmRsZXItcnVudGltZS9kaXN0L2NvbnN0YW50LmNqcy5qcz8xNTgyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIHNkayA9IHJlcXVpcmUoJ0Btb2R1bGUtZmVkZXJhdGlvbi9zZGsnKTtcblxuY29uc3QgRkVERVJBVElPTl9TVVBQT1JURURfVFlQRVMgPSBbXG4gICAgJ3NjcmlwdCdcbl07XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkVOQ09ERV9OQU1FX1BSRUZJWFwiLCB7XG5cdGVudW1lcmFibGU6IHRydWUsXG5cdGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gc2RrLkVOQ09ERV9OQU1FX1BSRUZJWDsgfVxufSk7XG5leHBvcnRzLkZFREVSQVRJT05fU1VQUE9SVEVEX1RZUEVTID0gRkVERVJBVElPTl9TVVBQT1JURURfVFlQRVM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///4179\n\n}");

/***/ }),

/***/ 4639:
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("{\n\n// Import bootstrap asynchronously to create proper async boundary for Module Federation\n__webpack_require__.e(/* import() */ 842).then(__webpack_require__.bind(__webpack_require__, 9842)).then(function (_ref) {\n  var bootstrap = _ref.bootstrap;\n  bootstrap({\n    container: 'root'\n  })[\"catch\"](function (error) {\n    console.error('Failed to bootstrap IF Party Master app:', error);\n  });\n})[\"catch\"](function (error) {\n  console.error('Failed to load bootstrap module:', error);\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDYzOS5qcyIsIm1hcHBpbmdzIjoiOztBQUFBO0FBQ0EsbUdBQXFCLENBQUNBLElBQUksQ0FBQyxVQUFBQyxJQUFBLEVBQWtCO0VBQUEsSUFBZkMsU0FBUyxHQUFBRCxJQUFBLENBQVRDLFNBQVM7RUFDckNBLFNBQVMsQ0FBQztJQUNSQyxTQUFTLEVBQUU7R0FDWixDQUFDLFNBQU0sQ0FBQyxVQUFBQyxLQUFLLEVBQUc7SUFDZkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsMENBQTBDLEVBQUVBLEtBQUssQ0FBQztFQUNsRSxDQUFDLENBQUM7QUFDSixDQUFDLENBQUMsU0FBTSxDQUFDLFVBQUFBLEtBQUssRUFBRztFQUNmQyxPQUFPLENBQUNELEtBQUssQ0FBQyxrQ0FBa0MsRUFBRUEsS0FBSyxDQUFDO0FBQzFELENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0Bjcm0vaWYtcGFydHktbWFzdGVyLy4vc3JjL21haW4udHN4PzQyYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSW1wb3J0IGJvb3RzdHJhcCBhc3luY2hyb25vdXNseSB0byBjcmVhdGUgcHJvcGVyIGFzeW5jIGJvdW5kYXJ5IGZvciBNb2R1bGUgRmVkZXJhdGlvblxuaW1wb3J0KCcuL2Jvb3RzdHJhcCcpLnRoZW4oKHsgYm9vdHN0cmFwIH0pID0+IHtcbiAgYm9vdHN0cmFwKHtcbiAgICBjb250YWluZXI6ICdyb290J1xuICB9KS5jYXRjaChlcnJvciA9PiB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGJvb3RzdHJhcCBJRiBQYXJ0eSBNYXN0ZXIgYXBwOicsIGVycm9yKTtcbiAgfSk7XG59KS5jYXRjaChlcnJvciA9PiB7XG4gIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGJvb3RzdHJhcCBtb2R1bGU6JywgZXJyb3IpO1xufSk7XG4iXSwibmFtZXMiOlsidGhlbiIsIl9yZWYiLCJib290c3RyYXAiLCJjb250YWluZXIiLCJlcnJvciIsImNvbnNvbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///4639\n\n}");

/***/ }),

/***/ 6689:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nfunction _object_without_properties_loose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\nexports._extends = _extends;\nexports._object_without_properties_loose = _object_without_properties_loose;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjY4OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSx1QkFBdUI7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdCQUFnQjtBQUNoQix3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL2lmLXBhcnR5LW1hc3Rlci8uLi8uLi9ub2RlX21vZHVsZXMvQG1vZHVsZS1mZWRlcmF0aW9uL3J1bnRpbWUtdG9vbHMvbm9kZV9tb2R1bGVzL0Btb2R1bGUtZmVkZXJhdGlvbi9ydW50aW1lL2Rpc3QvcG9seWZpbGxzLmNqcy5qcz85YTc5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gICAgX2V4dGVuZHMgPSBPYmplY3QuYXNzaWduIHx8IGZ1bmN0aW9uIGFzc2lnbih0YXJnZXQpIHtcbiAgICAgICAgZm9yKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKyl7XG4gICAgICAgICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldO1xuICAgICAgICAgICAgZm9yKHZhciBrZXkgaW4gc291cmNlKWlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0YXJnZXQ7XG4gICAgfTtcbiAgICByZXR1cm4gX2V4dGVuZHMuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZnVuY3Rpb24gX29iamVjdF93aXRob3V0X3Byb3BlcnRpZXNfbG9vc2Uoc291cmNlLCBleGNsdWRlZCkge1xuICAgIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuICAgIHZhciB0YXJnZXQgPSB7fTtcbiAgICB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7XG4gICAgdmFyIGtleSwgaTtcbiAgICBmb3IoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAga2V5ID0gc291cmNlS2V5c1tpXTtcbiAgICAgICAgaWYgKGV4Y2x1ZGVkLmluZGV4T2Yoa2V5KSA+PSAwKSBjb250aW51ZTtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICB9XG4gICAgcmV0dXJuIHRhcmdldDtcbn1cblxuZXhwb3J0cy5fZXh0ZW5kcyA9IF9leHRlbmRzO1xuZXhwb3J0cy5fb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZSA9IF9vYmplY3Rfd2l0aG91dF9wcm9wZXJ0aWVzX2xvb3NlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///6689\n\n}");

/***/ }),

/***/ 6817:
/***/ ((module) => {

eval("{\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/browser/index.ts\nvar browser_exports = {};\n__export(browser_exports, {\n  createLogger: () => createLogger2,\n  logger: () => logger\n});\nmodule.exports = __toCommonJS(browser_exports);\n\n// src/browser/color.ts\nvar supportsSubstitutions = void 0;\nvar supportColor = () => {\n  if (typeof supportsSubstitutions !== \"undefined\") {\n    return supportsSubstitutions;\n  }\n  try {\n    console.log(\"%c\", \"color:\");\n    supportsSubstitutions = true;\n  } catch (e) {\n    supportsSubstitutions = false;\n  }\n  return supportsSubstitutions;\n};\nvar ansiToCss = {\n  \"bold\": \"font-weight: bold;\",\n  \"red\": \"color: red;\",\n  \"green\": \"color: green;\",\n  \"orange\": \"color: orange;\",\n  \"dodgerblue\": \"color: dodgerblue;\",\n  \"magenta\": \"color: magenta;\",\n  \"gray\": \"color: gray;\"\n};\nvar formatter = (key) => supportColor() ? (input) => {\n  if (Array.isArray(input)) {\n    const [label, style] = input;\n    return [`%c${label.replace(\"%c\", \"\")}`, style ? `${ansiToCss[key]}${style}` : `${ansiToCss[key] || \"\"}`];\n  }\n  return [`%c${String(input).replace(\"%c\", \"\")}`, ansiToCss[key] || \"\"];\n} : (input) => [String(input)];\nvar bold = formatter(\"bold\");\nvar red = formatter(\"red\");\nvar green = formatter(\"green\");\nvar orange = formatter(\"orange\");\nvar dodgerblue = formatter(\"dodgerblue\");\nvar magenta = formatter(\"magenta\");\nvar gray = formatter(\"gray\");\n\n// src/browser/utils.ts\nfunction getLabel(type, logType, labels) {\n  let label = [\"\"];\n  if (\"label\" in logType) {\n    label = [labels[type] || logType.label || \"\"];\n    label = bold(logType.color ? logType.color(label) : label[0]);\n  }\n  label = label.filter(Boolean);\n  return label;\n}\nfunction finalLog(label, text, args, message) {\n  if (label.length) {\n    if (Array.isArray(message)) {\n      console.log(...label, ...message);\n    } else {\n      console.log(...label, text);\n    }\n  } else {\n    Array.isArray(message) ? console.log(...message) : console.log(text, ...args);\n  }\n}\n\n// src/constants.ts\nvar LOG_LEVEL = {\n  error: 0,\n  warn: 1,\n  info: 2,\n  log: 3,\n  verbose: 4\n};\n\n// src/utils.ts\nvar errorStackRegExp = /at\\s.*:\\d+:\\d+[\\s\\)]*$/;\nvar anonymousErrorStackRegExp = /at\\s.*\\(<anonymous>\\)$/;\nvar isErrorStackMessage = (message) => errorStackRegExp.test(message) || anonymousErrorStackRegExp.test(message);\n\n// src/createLogger.ts\nvar createLogger = (options = {}, { getLabel: getLabel2, handleError, finalLog: finalLog2, greet, LOG_TYPES: LOG_TYPES2 }) => {\n  let maxLevel = options.level || \"log\";\n  let customLabels = options.labels || {};\n  let log = (type, message, ...args) => {\n    if (LOG_LEVEL[LOG_TYPES2[type].level] > LOG_LEVEL[maxLevel]) {\n      return;\n    }\n    if (message === void 0 || message === null) {\n      return console.log();\n    }\n    let logType = LOG_TYPES2[type];\n    let text = \"\";\n    const label = getLabel2(type, logType, customLabels);\n    if (message instanceof Error) {\n      if (message.stack) {\n        let [name, ...rest] = message.stack.split(\"\\n\");\n        if (name.startsWith(\"Error: \")) {\n          name = name.slice(7);\n        }\n        text = `${name}\n${handleError(rest.join(\"\\n\"))}`;\n      } else {\n        text = message.message;\n      }\n    } else if (logType.level === \"error\" && typeof message === \"string\") {\n      let lines = message.split(\"\\n\");\n      text = lines.map((line) => isErrorStackMessage(line) ? handleError(line) : line).join(\"\\n\");\n    } else {\n      text = `${message}`;\n    }\n    finalLog2(label, text, args, message);\n  };\n  let logger2 = {\n    // greet\n    greet: (message) => log(\"log\", greet(message))\n  };\n  Object.keys(LOG_TYPES2).forEach((key) => {\n    logger2[key] = (...args) => log(key, ...args);\n  });\n  Object.defineProperty(logger2, \"level\", {\n    get: () => maxLevel,\n    set(val) {\n      maxLevel = val;\n    }\n  });\n  Object.defineProperty(logger2, \"labels\", {\n    get: () => customLabels,\n    set(val) {\n      customLabels = val;\n    }\n  });\n  logger2.override = (customLogger) => {\n    Object.assign(logger2, customLogger);\n  };\n  return logger2;\n};\n\n// src/browser/gradient.ts\nvar startColor = [189, 255, 243];\nvar endColor = [74, 194, 154];\nvar isWord = (char) => !/[\\s\\n]/.test(char);\nfunction gradient(message) {\n  if (!supportColor()) {\n    return [message];\n  }\n  const chars = [...message];\n  const words = chars.filter(isWord);\n  const steps = words.length - 1;\n  if (steps === 0) {\n    console.log(`%c${message}`, `color: rgb(${startColor.join(\",\")}); font-weight: bold;`);\n    return [message];\n  }\n  let output = \"\";\n  let styles = [];\n  chars.forEach((char) => {\n    if (isWord(char)) {\n      const progress = words.indexOf(char) / steps;\n      const r = Math.round(startColor[0] + (endColor[0] - startColor[0]) * progress);\n      const g = Math.round(startColor[1] + (endColor[1] - startColor[1]) * progress);\n      const b = Math.round(startColor[2] + (endColor[2] - startColor[2]) * progress);\n      output += `%c${char}`;\n      styles.push(`color: rgb(${r},${g},${b}); font-weight: bold;`);\n    } else {\n      output += char;\n    }\n  });\n  return [output, ...styles];\n}\n\n// src/browser/constants.ts\nvar LOG_TYPES = {\n  // Level error\n  error: {\n    label: \"error\",\n    level: \"error\",\n    color: red\n  },\n  // Level warn\n  warn: {\n    label: \"warn\",\n    level: \"warn\",\n    color: orange\n  },\n  // Level info\n  info: {\n    label: \"info\",\n    level: \"info\",\n    color: dodgerblue\n  },\n  start: {\n    label: \"start\",\n    level: \"info\",\n    color: dodgerblue\n  },\n  ready: {\n    label: \"ready\",\n    level: \"info\",\n    color: green\n  },\n  success: {\n    label: \"success\",\n    level: \"info\",\n    color: green\n  },\n  // Level log\n  log: {\n    level: \"log\"\n  },\n  // Level debug\n  debug: {\n    label: \"debug\",\n    level: \"verbose\",\n    color: magenta\n  }\n};\n\n// src/browser/createLogger.ts\nfunction createLogger2(options = {}) {\n  return createLogger(options, {\n    handleError: (msg) => msg,\n    getLabel,\n    gradient,\n    finalLog,\n    LOG_TYPES,\n    greet: (msg) => {\n      return gradient(msg);\n    }\n  });\n}\n\n// src/browser/index.ts\nvar logger = createLogger2();\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///6817\n\n}");

/***/ }),

/***/ 9399:
/***/ ((__unused_webpack_module, exports) => {

eval("{\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nexports._extends = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTM5OS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQkFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AY3JtL2lmLXBhcnR5LW1hc3Rlci8uLi8uLi9ub2RlX21vZHVsZXMvQG1vZHVsZS1mZWRlcmF0aW9uL3dlYnBhY2stYnVuZGxlci1ydW50aW1lL2Rpc3QvcG9seWZpbGxzLmNqcy5qcz9kODZmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gICAgX2V4dGVuZHMgPSBPYmplY3QuYXNzaWduIHx8IGZ1bmN0aW9uIGFzc2lnbih0YXJnZXQpIHtcbiAgICAgICAgZm9yKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKyl7XG4gICAgICAgICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldO1xuICAgICAgICAgICAgZm9yKHZhciBrZXkgaW4gc291cmNlKWlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0YXJnZXQ7XG4gICAgfTtcbiAgICByZXR1cm4gX2V4dGVuZHMuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0cy5fZXh0ZW5kcyA9IF9leHRlbmRzO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///9399\n\n}");

/***/ }),

/***/ 9468:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nvar isomorphicRslog = __webpack_require__(6817);\nvar polyfills = __webpack_require__(1784);\n\nconst FederationModuleManifest = 'federation-manifest.json';\nconst MANIFEST_EXT = '.json';\nconst BROWSER_LOG_KEY = 'FEDERATION_DEBUG';\nconst BROWSER_LOG_VALUE = '1';\nconst NameTransformSymbol = {\n    AT: '@',\n    HYPHEN: '-',\n    SLASH: '/'\n};\nconst NameTransformMap = {\n    [NameTransformSymbol.AT]: 'scope_',\n    [NameTransformSymbol.HYPHEN]: '_',\n    [NameTransformSymbol.SLASH]: '__'\n};\nconst EncodedNameTransformMap = {\n    [NameTransformMap[NameTransformSymbol.AT]]: NameTransformSymbol.AT,\n    [NameTransformMap[NameTransformSymbol.HYPHEN]]: NameTransformSymbol.HYPHEN,\n    [NameTransformMap[NameTransformSymbol.SLASH]]: NameTransformSymbol.SLASH\n};\nconst SEPARATOR = ':';\nconst ManifestFileName = 'mf-manifest.json';\nconst StatsFileName = 'mf-stats.json';\nconst MFModuleType = {\n    NPM: 'npm',\n    APP: 'app'\n};\nconst MODULE_DEVTOOL_IDENTIFIER = '__MF_DEVTOOLS_MODULE_INFO__';\nconst ENCODE_NAME_PREFIX = 'ENCODE_NAME_PREFIX';\nconst TEMP_DIR = '.federation';\nconst MFPrefetchCommon = {\n    identifier: 'MFDataPrefetch',\n    globalKey: '__PREFETCH__',\n    library: 'mf-data-prefetch',\n    exportsKey: '__PREFETCH_EXPORTS__',\n    fileName: 'bootstrap.js'\n};\n\nvar ContainerPlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar ContainerReferencePlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar ModuleFederationPlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nvar SharePlugin = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nfunction isBrowserEnv() {\n    return typeof window !== 'undefined';\n}\nfunction isBrowserDebug() {\n    try {\n        if (isBrowserEnv() && window.localStorage) {\n            return localStorage.getItem(BROWSER_LOG_KEY) === BROWSER_LOG_VALUE;\n        }\n    } catch (error) {\n        return false;\n    }\n    return false;\n}\nfunction isDebugMode() {\n    if (typeof process !== 'undefined' && process.env && process.env['FEDERATION_DEBUG']) {\n        return Boolean(process.env['FEDERATION_DEBUG']);\n    }\n    if (typeof FEDERATION_DEBUG !== 'undefined' && Boolean(FEDERATION_DEBUG)) {\n        return true;\n    }\n    return isBrowserDebug();\n}\nconst getProcessEnv = function() {\n    return typeof process !== 'undefined' && process.env ? process.env : {};\n};\n\nconst PREFIX = '[ Module Federation ]';\nfunction setDebug(loggerInstance) {\n    if (isDebugMode()) {\n        loggerInstance.level = 'verbose';\n    }\n}\nfunction setPrefix(loggerInstance, prefix) {\n    loggerInstance.labels = {\n        warn: `${prefix} Warn`,\n        error: `${prefix} Error`,\n        success: `${prefix} Success`,\n        info: `${prefix} Info`,\n        ready: `${prefix} Ready`,\n        debug: `${prefix} Debug`\n    };\n}\nfunction createLogger(prefix) {\n    const loggerInstance = isomorphicRslog.createLogger({\n        labels: {\n            warn: `${PREFIX} Warn`,\n            error: `${PREFIX} Error`,\n            success: `${PREFIX} Success`,\n            info: `${PREFIX} Info`,\n            ready: `${PREFIX} Ready`,\n            debug: `${PREFIX} Debug`\n        }\n    });\n    setDebug(loggerInstance);\n    setPrefix(loggerInstance, prefix);\n    return loggerInstance;\n}\nconst logger = createLogger(PREFIX);\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// entry: name:version   version : 1.0.0 | ^1.2.3\n// entry: name:entry  entry:  https://localhost:9000/federation-manifest.json\nconst parseEntry = (str, devVerOrUrl, separator = SEPARATOR)=>{\n    const strSplit = str.split(separator);\n    const devVersionOrUrl = getProcessEnv()['NODE_ENV'] === 'development' && devVerOrUrl;\n    const defaultVersion = '*';\n    const isEntry = (s)=>s.startsWith('http') || s.includes(MANIFEST_EXT);\n    // Check if the string starts with a type\n    if (strSplit.length >= 2) {\n        let [name, ...versionOrEntryArr] = strSplit;\n        if (str.startsWith(separator)) {\n            versionOrEntryArr = [\n                devVersionOrUrl || strSplit.slice(-1)[0]\n            ];\n            name = strSplit.slice(0, -1).join(separator);\n        }\n        let versionOrEntry = devVersionOrUrl || versionOrEntryArr.join(separator);\n        if (isEntry(versionOrEntry)) {\n            return {\n                name,\n                entry: versionOrEntry\n            };\n        } else {\n            // Apply version rule\n            // devVersionOrUrl => inputVersion => defaultVersion\n            return {\n                name,\n                version: versionOrEntry || defaultVersion\n            };\n        }\n    } else if (strSplit.length === 1) {\n        const [name] = strSplit;\n        if (devVersionOrUrl && isEntry(devVersionOrUrl)) {\n            return {\n                name,\n                entry: devVersionOrUrl\n            };\n        }\n        return {\n            name,\n            version: devVersionOrUrl || defaultVersion\n        };\n    } else {\n        throw `Invalid entry value: ${str}`;\n    }\n};\nconst composeKeyWithSeparator = function(...args) {\n    if (!args.length) {\n        return '';\n    }\n    return args.reduce((sum, cur)=>{\n        if (!cur) {\n            return sum;\n        }\n        if (!sum) {\n            return cur;\n        }\n        return `${sum}${SEPARATOR}${cur}`;\n    }, '');\n};\nconst encodeName = function(name, prefix = '', withExt = false) {\n    try {\n        const ext = withExt ? '.js' : '';\n        return `${prefix}${name.replace(new RegExp(`${NameTransformSymbol.AT}`, 'g'), NameTransformMap[NameTransformSymbol.AT]).replace(new RegExp(`${NameTransformSymbol.HYPHEN}`, 'g'), NameTransformMap[NameTransformSymbol.HYPHEN]).replace(new RegExp(`${NameTransformSymbol.SLASH}`, 'g'), NameTransformMap[NameTransformSymbol.SLASH])}${ext}`;\n    } catch (err) {\n        throw err;\n    }\n};\nconst decodeName = function(name, prefix, withExt) {\n    try {\n        let decodedName = name;\n        if (prefix) {\n            if (!decodedName.startsWith(prefix)) {\n                return decodedName;\n            }\n            decodedName = decodedName.replace(new RegExp(prefix, 'g'), '');\n        }\n        decodedName = decodedName.replace(new RegExp(`${NameTransformMap[NameTransformSymbol.AT]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.AT]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.SLASH]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.SLASH]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.HYPHEN]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.HYPHEN]]);\n        if (withExt) {\n            decodedName = decodedName.replace('.js', '');\n        }\n        return decodedName;\n    } catch (err) {\n        throw err;\n    }\n};\nconst generateExposeFilename = (exposeName, withExt)=>{\n    if (!exposeName) {\n        return '';\n    }\n    let expose = exposeName;\n    if (expose === '.') {\n        expose = 'default_export';\n    }\n    if (expose.startsWith('./')) {\n        expose = expose.replace('./', '');\n    }\n    return encodeName(expose, '__federation_expose_', withExt);\n};\nconst generateShareFilename = (pkgName, withExt)=>{\n    if (!pkgName) {\n        return '';\n    }\n    return encodeName(pkgName, '__federation_shared_', withExt);\n};\nconst getResourceUrl = (module, sourceUrl)=>{\n    if ('getPublicPath' in module) {\n        let publicPath;\n        if (!module.getPublicPath.startsWith('function')) {\n            publicPath = new Function(module.getPublicPath)();\n        } else {\n            publicPath = new Function('return ' + module.getPublicPath)()();\n        }\n        return `${publicPath}${sourceUrl}`;\n    } else if ('publicPath' in module) {\n        return `${module.publicPath}${sourceUrl}`;\n    } else {\n        console.warn('Cannot get resource URL. If in debug mode, please ignore.', module, sourceUrl);\n        return '';\n    }\n};\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nconst assert = (condition, msg)=>{\n    if (!condition) {\n        error(msg);\n    }\n};\nconst error = (msg)=>{\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n};\nconst warn = (msg)=>{\n    console.warn(`${LOG_CATEGORY}: ${msg}`);\n};\nfunction safeToString(info) {\n    try {\n        return JSON.stringify(info, null, 2);\n    } catch (e) {\n        return '';\n    }\n}\n// RegExp for version string\nconst VERSION_PATTERN_REGEXP = /^([\\d^=v<>~]|[*xX]$)/;\nfunction isRequiredVersion(str) {\n    return VERSION_PATTERN_REGEXP.test(str);\n}\n\nconst simpleJoinRemoteEntry = (rPath, rName)=>{\n    if (!rPath) {\n        return rName;\n    }\n    const transformPath = (str)=>{\n        if (str === '.') {\n            return '';\n        }\n        if (str.startsWith('./')) {\n            return str.replace('./', '');\n        }\n        if (str.startsWith('/')) {\n            const strWithoutSlash = str.slice(1);\n            if (strWithoutSlash.endsWith('/')) {\n                return strWithoutSlash.slice(0, -1);\n            }\n            return strWithoutSlash;\n        }\n        return str;\n    };\n    const transformedPath = transformPath(rPath);\n    if (!transformedPath) {\n        return rName;\n    }\n    if (transformedPath.endsWith('/')) {\n        return `${transformedPath}${rName}`;\n    }\n    return `${transformedPath}/${rName}`;\n};\nfunction inferAutoPublicPath(url) {\n    return url.replace(/#.*$/, '').replace(/\\?.*$/, '').replace(/\\/[^\\/]+$/, '/');\n}\n// Priority: overrides > remotes\n// eslint-disable-next-line max-lines-per-function\nfunction generateSnapshotFromManifest(manifest, options = {}) {\n    var _manifest_metaData, _manifest_metaData1;\n    const { remotes = {}, overrides = {}, version } = options;\n    let remoteSnapshot;\n    const getPublicPath = ()=>{\n        if ('publicPath' in manifest.metaData) {\n            if (manifest.metaData.publicPath === 'auto' && version) {\n                // use same implementation as publicPath auto runtime module implements\n                return inferAutoPublicPath(version);\n            }\n            return manifest.metaData.publicPath;\n        } else {\n            return manifest.metaData.getPublicPath;\n        }\n    };\n    const overridesKeys = Object.keys(overrides);\n    let remotesInfo = {};\n    // If remotes are not provided, only the remotes in the manifest will be read\n    if (!Object.keys(remotes).length) {\n        var _manifest_remotes;\n        remotesInfo = ((_manifest_remotes = manifest.remotes) == null ? void 0 : _manifest_remotes.reduce((res, next)=>{\n            let matchedVersion;\n            const name = next.federationContainerName;\n            // overrides have higher priority\n            if (overridesKeys.includes(name)) {\n                matchedVersion = overrides[name];\n            } else {\n                if ('version' in next) {\n                    matchedVersion = next.version;\n                } else {\n                    matchedVersion = next.entry;\n                }\n            }\n            res[name] = {\n                matchedVersion\n            };\n            return res;\n        }, {})) || {};\n    }\n    // If remotes (deploy scenario) are specified, they need to be traversed again\n    Object.keys(remotes).forEach((key)=>remotesInfo[key] = {\n            // overrides will override dependencies\n            matchedVersion: overridesKeys.includes(key) ? overrides[key] : remotes[key]\n        });\n    const { remoteEntry: { path: remoteEntryPath, name: remoteEntryName, type: remoteEntryType }, types: remoteTypes, buildInfo: { buildVersion }, globalName, ssrRemoteEntry } = manifest.metaData;\n    const { exposes } = manifest;\n    let basicRemoteSnapshot = {\n        version: version ? version : '',\n        buildVersion,\n        globalName,\n        remoteEntry: simpleJoinRemoteEntry(remoteEntryPath, remoteEntryName),\n        remoteEntryType,\n        remoteTypes: simpleJoinRemoteEntry(remoteTypes.path, remoteTypes.name),\n        remoteTypesZip: remoteTypes.zip || '',\n        remoteTypesAPI: remoteTypes.api || '',\n        remotesInfo,\n        shared: manifest == null ? void 0 : manifest.shared.map((item)=>({\n                assets: item.assets,\n                sharedName: item.name,\n                version: item.version\n            })),\n        modules: exposes == null ? void 0 : exposes.map((expose)=>({\n                moduleName: expose.name,\n                modulePath: expose.path,\n                assets: expose.assets\n            }))\n    };\n    if ((_manifest_metaData = manifest.metaData) == null ? void 0 : _manifest_metaData.prefetchInterface) {\n        const prefetchInterface = manifest.metaData.prefetchInterface;\n        basicRemoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            prefetchInterface\n        });\n    }\n    if ((_manifest_metaData1 = manifest.metaData) == null ? void 0 : _manifest_metaData1.prefetchEntry) {\n        const { path, name, type } = manifest.metaData.prefetchEntry;\n        basicRemoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            prefetchEntry: simpleJoinRemoteEntry(path, name),\n            prefetchEntryType: type\n        });\n    }\n    if ('publicPath' in manifest.metaData) {\n        remoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            publicPath: getPublicPath()\n        });\n    } else {\n        remoteSnapshot = polyfills._extends({}, basicRemoteSnapshot, {\n            getPublicPath: getPublicPath()\n        });\n    }\n    if (ssrRemoteEntry) {\n        const fullSSRRemoteEntry = simpleJoinRemoteEntry(ssrRemoteEntry.path, ssrRemoteEntry.name);\n        remoteSnapshot.ssrRemoteEntry = fullSSRRemoteEntry;\n        remoteSnapshot.ssrRemoteEntryType = ssrRemoteEntry.type || 'commonjs-module';\n    }\n    return remoteSnapshot;\n}\nfunction isManifestProvider(moduleInfo) {\n    if ('remoteEntry' in moduleInfo && moduleInfo.remoteEntry.includes(MANIFEST_EXT)) {\n        return true;\n    } else {\n        return false;\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function safeWrapper(callback, disableWarn) {\n    try {\n        const res = await callback();\n        return res;\n    } catch (e) {\n        !disableWarn && warn(e);\n        return;\n    }\n}\nfunction isStaticResourcesEqual(url1, url2) {\n    const REG_EXP = /^(https?:)?\\/\\//i;\n    // Transform url1 and url2 into relative paths\n    const relativeUrl1 = url1.replace(REG_EXP, '').replace(/\\/$/, '');\n    const relativeUrl2 = url2.replace(REG_EXP, '').replace(/\\/$/, '');\n    // Check if the relative paths are identical\n    return relativeUrl1 === relativeUrl2;\n}\nfunction createScript(info) {\n    // Retrieve the existing script element by its src attribute\n    let script = null;\n    let needAttach = true;\n    let timeout = 20000;\n    let timeoutId;\n    const scripts = document.getElementsByTagName('script');\n    for(let i = 0; i < scripts.length; i++){\n        const s = scripts[i];\n        const scriptSrc = s.getAttribute('src');\n        if (scriptSrc && isStaticResourcesEqual(scriptSrc, info.url)) {\n            script = s;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!script) {\n        const attrs = info.attrs;\n        script = document.createElement('script');\n        script.type = (attrs == null ? void 0 : attrs['type']) === 'module' ? 'module' : 'text/javascript';\n        let createScriptRes = undefined;\n        if (info.createScriptHook) {\n            createScriptRes = info.createScriptHook(info.url, info.attrs);\n            if (createScriptRes instanceof HTMLScriptElement) {\n                script = createScriptRes;\n            } else if (typeof createScriptRes === 'object') {\n                if ('script' in createScriptRes && createScriptRes.script) {\n                    script = createScriptRes.script;\n                }\n                if ('timeout' in createScriptRes && createScriptRes.timeout) {\n                    timeout = createScriptRes.timeout;\n                }\n            }\n        }\n        if (!script.src) {\n            script.src = info.url;\n        }\n        if (attrs && !createScriptRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (script) {\n                    if (name === 'async' || name === 'defer') {\n                        script[name] = attrs[name];\n                    // Attributes that do not exist are considered overridden\n                    } else if (!script.getAttribute(name)) {\n                        script.setAttribute(name, attrs[name]);\n                    }\n                }\n            });\n        }\n    }\n    const onScriptComplete = async (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        var _info_cb;\n        clearTimeout(timeoutId);\n        // Prevent memory leaks in IE.\n        if (script) {\n            script.onerror = null;\n            script.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteScript = true } = info;\n                if (needDeleteScript) {\n                    (script == null ? void 0 : script.parentNode) && script.parentNode.removeChild(script);\n                }\n            });\n            if (prev && typeof prev === 'function') {\n                var _info_cb1;\n                const result = prev(event);\n                if (result instanceof Promise) {\n                    var _info_cb2;\n                    const res = await result;\n                    info == null ? void 0 : (_info_cb2 = info.cb) == null ? void 0 : _info_cb2.call(info);\n                    return res;\n                }\n                info == null ? void 0 : (_info_cb1 = info.cb) == null ? void 0 : _info_cb1.call(info);\n                return result;\n            }\n        }\n        info == null ? void 0 : (_info_cb = info.cb) == null ? void 0 : _info_cb.call(info);\n    };\n    script.onerror = onScriptComplete.bind(null, script.onerror);\n    script.onload = onScriptComplete.bind(null, script.onload);\n    timeoutId = setTimeout(()=>{\n        onScriptComplete(null, new Error(`Remote script \"${info.url}\" time-outed.`));\n    }, timeout);\n    return {\n        script,\n        needAttach\n    };\n}\nfunction createLink(info) {\n    // <link rel=\"preload\" href=\"script.js\" as=\"script\">\n    // Retrieve the existing script element by its src attribute\n    let link = null;\n    let needAttach = true;\n    const links = document.getElementsByTagName('link');\n    for(let i = 0; i < links.length; i++){\n        const l = links[i];\n        const linkHref = l.getAttribute('href');\n        const linkRef = l.getAttribute('ref');\n        if (linkHref && isStaticResourcesEqual(linkHref, info.url) && linkRef === info.attrs['ref']) {\n            link = l;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!link) {\n        link = document.createElement('link');\n        link.setAttribute('href', info.url);\n        let createLinkRes = undefined;\n        const attrs = info.attrs;\n        if (info.createLinkHook) {\n            createLinkRes = info.createLinkHook(info.url, attrs);\n            if (createLinkRes instanceof HTMLLinkElement) {\n                link = createLinkRes;\n            }\n        }\n        if (attrs && !createLinkRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (link && !link.getAttribute(name)) {\n                    link.setAttribute(name, attrs[name]);\n                }\n            });\n        }\n    }\n    const onLinkComplete = (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        // Prevent memory leaks in IE.\n        if (link) {\n            link.onerror = null;\n            link.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteLink = true } = info;\n                if (needDeleteLink) {\n                    (link == null ? void 0 : link.parentNode) && link.parentNode.removeChild(link);\n                }\n            });\n            if (prev) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const res = prev(event);\n                info.cb();\n                return res;\n            }\n        }\n        info.cb();\n    };\n    link.onerror = onLinkComplete.bind(null, link.onerror);\n    link.onload = onLinkComplete.bind(null, link.onload);\n    return {\n        link,\n        needAttach\n    };\n}\nfunction loadScript(url, info) {\n    const { attrs = {}, createScriptHook } = info;\n    return new Promise((resolve, _reject)=>{\n        const { script, needAttach } = createScript({\n            url,\n            cb: resolve,\n            attrs: polyfills._extends({\n                fetchpriority: 'high'\n            }, attrs),\n            createScriptHook,\n            needDeleteScript: true\n        });\n        needAttach && document.head.appendChild(script);\n    });\n}\n\nfunction importNodeModule(name) {\n    if (!name) {\n        throw new Error('import specifier is required');\n    }\n    const importModule = new Function('name', `return import(name)`);\n    return importModule(name).then((res)=>res).catch((error)=>{\n        console.error(`Error importing module ${name}:`, error);\n        throw error;\n    });\n}\nconst loadNodeFetch = async ()=>{\n    const fetchModule = await importNodeModule('node-fetch');\n    return fetchModule.default || fetchModule;\n};\nconst lazyLoaderHookFetch = async (input, init)=>{\n    // @ts-ignore\n    const loaderHooks = __webpack_require__.federation.instance.loaderHook;\n    const hook = (url, init)=>{\n        return loaderHooks.lifecycle.fetch.emit(url, init);\n    };\n    const res = await hook(input, init || {});\n    if (!res || !(res instanceof Response)) {\n        const fetchFunction = typeof fetch === 'undefined' ? await loadNodeFetch() : fetch;\n        return fetchFunction(input, init || {});\n    }\n    return res;\n};\nfunction createScriptNode(url, cb, attrs, createScriptHook) {\n    if (createScriptHook) {\n        const hookResult = createScriptHook(url);\n        if (hookResult && typeof hookResult === 'object' && 'url' in hookResult) {\n            url = hookResult.url;\n        }\n    }\n    let urlObj;\n    try {\n        urlObj = new URL(url);\n    } catch (e) {\n        console.error('Error constructing URL:', e);\n        cb(new Error(`Invalid URL: ${e}`));\n        return;\n    }\n    const getFetch = async ()=>{\n        //@ts-ignore\n        if (true) {\n            try {\n                //@ts-ignore\n                const loaderHooks = __webpack_require__.federation.instance.loaderHook;\n                if (loaderHooks.lifecycle.fetch) {\n                    return lazyLoaderHookFetch;\n                }\n            } catch (e) {\n                console.warn('federation.instance.loaderHook.lifecycle.fetch failed:', e);\n            }\n        }\n        return typeof fetch === 'undefined' ? loadNodeFetch() : fetch;\n    };\n    const handleScriptFetch = async (f, urlObj)=>{\n        try {\n            var //@ts-ignore\n            _vm_constants;\n            const res = await f(urlObj.href);\n            const data = await res.text();\n            const [path, vm] = await Promise.all([\n                importNodeModule('path'),\n                importNodeModule('vm')\n            ]);\n            const scriptContext = {\n                exports: {},\n                module: {\n                    exports: {}\n                }\n            };\n            const urlDirname = urlObj.pathname.split('/').slice(0, -1).join('/');\n            const filename = path.basename(urlObj.pathname);\n            var _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER;\n            const script = new vm.Script(`(function(exports, module, require, __dirname, __filename) {${data}\\n})`, {\n                filename,\n                importModuleDynamically: (_vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER = (_vm_constants = vm.constants) == null ? void 0 : _vm_constants.USE_MAIN_CONTEXT_DEFAULT_LOADER) != null ? _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER : importNodeModule\n            });\n            script.runInThisContext()(scriptContext.exports, scriptContext.module, eval('require'), urlDirname, filename);\n            const exportedInterface = scriptContext.module.exports || scriptContext.exports;\n            if (attrs && exportedInterface && attrs['globalName']) {\n                const container = exportedInterface[attrs['globalName']] || exportedInterface;\n                cb(undefined, container);\n                return;\n            }\n            cb(undefined, exportedInterface);\n        } catch (e) {\n            cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n        }\n    };\n    getFetch().then(async (f)=>{\n        if ((attrs == null ? void 0 : attrs['type']) === 'esm' || (attrs == null ? void 0 : attrs['type']) === 'module') {\n            return loadModule(urlObj.href, {\n                fetch: f,\n                vm: await importNodeModule('vm')\n            }).then(async (module)=>{\n                await module.evaluate();\n                cb(undefined, module.namespace);\n            }).catch((e)=>{\n                cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n            });\n        }\n        handleScriptFetch(f, urlObj);\n    }).catch((err)=>{\n        cb(err);\n    });\n}\nfunction loadScriptNode(url, info) {\n    return new Promise((resolve, reject)=>{\n        createScriptNode(url, (error, scriptContext)=>{\n            if (error) {\n                reject(error);\n            } else {\n                var _info_attrs, _info_attrs1;\n                const remoteEntryKey = (info == null ? void 0 : (_info_attrs = info.attrs) == null ? void 0 : _info_attrs['globalName']) || `__FEDERATION_${info == null ? void 0 : (_info_attrs1 = info.attrs) == null ? void 0 : _info_attrs1['name']}:custom__`;\n                const entryExports = globalThis[remoteEntryKey] = scriptContext;\n                resolve(entryExports);\n            }\n        }, info.attrs, info.createScriptHook);\n    });\n}\nasync function loadModule(url, options) {\n    const { fetch: fetch1, vm } = options;\n    const response = await fetch1(url);\n    const code = await response.text();\n    const module = new vm.SourceTextModule(code, {\n        // @ts-ignore\n        importModuleDynamically: async (specifier, script)=>{\n            const resolvedUrl = new URL(specifier, url).href;\n            return loadModule(resolvedUrl, options);\n        }\n    });\n    await module.link(async (specifier)=>{\n        const resolvedUrl = new URL(specifier, url).href;\n        const module = await loadModule(resolvedUrl, options);\n        return module;\n    });\n    return module;\n}\n\nfunction normalizeOptions(enableDefault, defaultOptions, key) {\n    return function(options) {\n        if (options === false) {\n            return false;\n        }\n        if (typeof options === 'undefined') {\n            if (enableDefault) {\n                return defaultOptions;\n            } else {\n                return false;\n            }\n        }\n        if (options === true) {\n            return defaultOptions;\n        }\n        if (options && typeof options === 'object') {\n            return polyfills._extends({}, defaultOptions, options);\n        }\n        throw new Error(`Unexpected type for \\`${key}\\`, expect boolean/undefined/object, got: ${typeof options}`);\n    };\n}\n\nexports.BROWSER_LOG_KEY = BROWSER_LOG_KEY;\nexports.BROWSER_LOG_VALUE = BROWSER_LOG_VALUE;\nexports.ENCODE_NAME_PREFIX = ENCODE_NAME_PREFIX;\nexports.EncodedNameTransformMap = EncodedNameTransformMap;\nexports.FederationModuleManifest = FederationModuleManifest;\nexports.MANIFEST_EXT = MANIFEST_EXT;\nexports.MFModuleType = MFModuleType;\nexports.MFPrefetchCommon = MFPrefetchCommon;\nexports.MODULE_DEVTOOL_IDENTIFIER = MODULE_DEVTOOL_IDENTIFIER;\nexports.ManifestFileName = ManifestFileName;\nexports.NameTransformMap = NameTransformMap;\nexports.NameTransformSymbol = NameTransformSymbol;\nexports.SEPARATOR = SEPARATOR;\nexports.StatsFileName = StatsFileName;\nexports.TEMP_DIR = TEMP_DIR;\nexports.assert = assert;\nexports.composeKeyWithSeparator = composeKeyWithSeparator;\nexports.containerPlugin = ContainerPlugin;\nexports.containerReferencePlugin = ContainerReferencePlugin;\nexports.createLink = createLink;\nexports.createLogger = createLogger;\nexports.createScript = createScript;\nexports.createScriptNode = createScriptNode;\nexports.decodeName = decodeName;\nexports.encodeName = encodeName;\nexports.error = error;\nexports.generateExposeFilename = generateExposeFilename;\nexports.generateShareFilename = generateShareFilename;\nexports.generateSnapshotFromManifest = generateSnapshotFromManifest;\nexports.getProcessEnv = getProcessEnv;\nexports.getResourceUrl = getResourceUrl;\nexports.inferAutoPublicPath = inferAutoPublicPath;\nexports.isBrowserEnv = isBrowserEnv;\nexports.isDebugMode = isDebugMode;\nexports.isManifestProvider = isManifestProvider;\nexports.isRequiredVersion = isRequiredVersion;\nexports.isStaticResourcesEqual = isStaticResourcesEqual;\nexports.loadScript = loadScript;\nexports.loadScriptNode = loadScriptNode;\nexports.logger = logger;\nexports.moduleFederationPlugin = ModuleFederationPlugin;\nexports.normalizeOptions = normalizeOptions;\nexports.parseEntry = parseEntry;\nexports.safeToString = safeToString;\nexports.safeWrapper = safeWrapper;\nexports.sharePlugin = SharePlugin;\nexports.simpleJoinRemoteEntry = simpleJoinRemoteEntry;\nexports.warn = warn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///9468\n\n}");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var execOptions = { id: moduleId, module: module, factory: __webpack_modules__[moduleId], require: __webpack_require__ };
/******/ 		__webpack_require__.i.forEach(function(handler) { handler(execOptions); });
/******/ 		module = execOptions.module;
/******/ 		execOptions.factory.call(module.exports, module, module.exports, execOptions.require);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = __webpack_module_cache__;
/******/ 	
/******/ 	// expose the module execution interceptor
/******/ 	__webpack_require__.i = [];
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/federation runtime */
/******/ 	(() => {
/******/ 		if(!__webpack_require__.federation){
/******/ 			__webpack_require__.federation = {
/******/ 				initOptions: {"name":"ifPartyMaster","remotes":[],"shareStrategy":"version-first"},
/******/ 				chunkMatcher: function(chunkId) {return true},
/******/ 				rootOutputDir: "",
/******/ 				initialConsumes: undefined,
/******/ 				bundlerRuntimeOptions: {}
/******/ 			};
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/load script */
/******/ 	(() => {
/******/ 		var inProgress = {};
/******/ 		var dataWebpackPrefix = "@crm/if-party-master:";
/******/ 		// loadScript function to load a script via script tag
/******/ 		__webpack_require__.l = (url, done, key, chunkId) => {
/******/ 			if(inProgress[url]) { inProgress[url].push(done); return; }
/******/ 			var script, needAttach;
/******/ 			if(key !== undefined) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				for(var i = 0; i < scripts.length; i++) {
/******/ 					var s = scripts[i];
/******/ 					if(s.getAttribute("src") == url || s.getAttribute("data-webpack") == dataWebpackPrefix + key) { script = s; break; }
/******/ 				}
/******/ 			}
/******/ 			if(!script) {
/******/ 				needAttach = true;
/******/ 				script = document.createElement('script');
/******/ 		
/******/ 				script.charset = 'utf-8';
/******/ 				script.timeout = 120;
/******/ 				if (__webpack_require__.nc) {
/******/ 					script.setAttribute("nonce", __webpack_require__.nc);
/******/ 				}
/******/ 				script.setAttribute("data-webpack", dataWebpackPrefix + key);
/******/ 		
/******/ 				script.src = url;
/******/ 			}
/******/ 			inProgress[url] = [done];
/******/ 			var onScriptComplete = (prev, event) => {
/******/ 				// avoid mem leaks in IE.
/******/ 				script.onerror = script.onload = null;
/******/ 				clearTimeout(timeout);
/******/ 				var doneFns = inProgress[url];
/******/ 				delete inProgress[url];
/******/ 				script.parentNode && script.parentNode.removeChild(script);
/******/ 				doneFns && doneFns.forEach((fn) => (fn(event)));
/******/ 				if(prev) return prev(event);
/******/ 			}
/******/ 			var timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);
/******/ 			script.onerror = onScriptComplete.bind(null, script.onerror);
/******/ 			script.onload = onScriptComplete.bind(null, script.onload);
/******/ 			needAttach && document.head.appendChild(script);
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.nmd = (module) => {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/sharing */
/******/ 	(() => {
/******/ 		__webpack_require__.S = {};
/******/ 		var initPromises = {};
/******/ 		var initTokens = {};
/******/ 		__webpack_require__.I = (name, initScope) => {
/******/ 			if(!initScope) initScope = [];
/******/ 			// handling circular init calls
/******/ 			var initToken = initTokens[name];
/******/ 			if(!initToken) initToken = initTokens[name] = {};
/******/ 			if(initScope.indexOf(initToken) >= 0) return;
/******/ 			initScope.push(initToken);
/******/ 			// only runs once
/******/ 			if(initPromises[name]) return initPromises[name];
/******/ 			// creates a new share scope if needed
/******/ 			if(!__webpack_require__.o(__webpack_require__.S, name)) __webpack_require__.S[name] = {};
/******/ 			// runs all init snippets from all modules reachable
/******/ 			var scope = __webpack_require__.S[name];
/******/ 			var warn = (msg) => {
/******/ 				if (typeof console !== "undefined" && console.warn) console.warn(msg);
/******/ 			};
/******/ 			var uniqueName = "@crm/if-party-master";
/******/ 			var register = (name, version, factory, eager) => {
/******/ 				var versions = scope[name] = scope[name] || {};
/******/ 				var activeVersion = versions[version];
/******/ 				if(!activeVersion || (!activeVersion.loaded && (!eager != !activeVersion.eager ? eager : uniqueName > activeVersion.from))) versions[version] = { get: factory, from: uniqueName, eager: !!eager };
/******/ 			};
/******/ 			var initExternal = (id) => {
/******/ 				var handleError = (err) => (warn("Initialization of sharing external failed: " + err));
/******/ 				try {
/******/ 					var module = __webpack_require__(id);
/******/ 					if(!module) return;
/******/ 					var initFn = (module) => (module && module.init && module.init(__webpack_require__.S[name], initScope))
/******/ 					if(module.then) return promises.push(module.then(initFn, handleError));
/******/ 					var initResult = initFn(module);
/******/ 					if(initResult && initResult.then) return promises.push(initResult['catch'](handleError));
/******/ 				} catch(err) { handleError(err); }
/******/ 			}
/******/ 			var promises = [];
/******/ 			switch(name) {
/******/ 				case "default": {
/******/ 					register("react-dom/client", "18.3.1", () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))));
/******/ 					register("react-dom", "18.3.1", () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))));
/******/ 					register("react/jsx-runtime", "18.3.1", () => (__webpack_require__.e(85).then(() => (() => (__webpack_require__(1085))))));
/******/ 					register("react", "18.3.1", () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))));
/******/ 				}
/******/ 				break;
/******/ 			}
/******/ 			if(!promises.length) return initPromises[name] = 1;
/******/ 			return initPromises[name] = Promise.all(promises).then(() => (initPromises[name] = 1));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/sharing */
/******/ 	(() => {
/******/ 		__webpack_require__.federation.initOptions.shared = {	"react-dom/client": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react-dom": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react/jsx-runtime": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(85).then(() => (() => (__webpack_require__(1085))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],	"react": [{	version: "18.3.1",
/******/ 				get: () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))),
/******/ 				scope: ["default"],
/******/ 				shareConfig: {"eager":false,"requiredVersion":"^18.0.0","strictVersion":false,"singleton":true}},],}
/******/ 		__webpack_require__.S = {};
/******/ 		var initPromises = {};
/******/ 		var initTokens = {};
/******/ 		__webpack_require__.I = (name, initScope) => {
/******/ 			return __webpack_require__.federation.bundlerRuntime.I({	shareScopeName: name,
/******/ 				webpackRequire: __webpack_require__,
/******/ 				initPromises: initPromises,
/******/ 				initTokens: initTokens,
/******/ 				initScope: initScope,
/******/ 			})
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		var scriptUrl;
/******/ 		if (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + "";
/******/ 		var document = __webpack_require__.g.document;
/******/ 		if (!scriptUrl && document) {
/******/ 			if (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')
/******/ 				scriptUrl = document.currentScript.src;
/******/ 			if (!scriptUrl) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				if(scripts.length) {
/******/ 					var i = scripts.length - 1;
/******/ 					while (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;
/******/ 				}
/******/ 			}
/******/ 		}
/******/ 		// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration
/******/ 		// or pass an empty string ("") and set the __webpack_public_path__ variable from your code to use your own logic.
/******/ 		if (!scriptUrl) throw new Error("Automatic publicPath is not supported in this browser");
/******/ 		scriptUrl = scriptUrl.replace(/^blob:/, "").replace(/#.*$/, "").replace(/\?.*$/, "").replace(/\/[^\/]+$/, "/");
/******/ 		__webpack_require__.p = scriptUrl;
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/consumes */
/******/ 	(() => {
/******/ 		var installedModules = {};
/******/ 		var moduleToHandlerMapping = {
/******/ 			6300: {
/******/ 				getter: () => (__webpack_require__.e(466).then(() => (() => (__webpack_require__(1085))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react/jsx-runtime",
/******/ 			},
/******/ 			3355: {
/******/ 				getter: () => (__webpack_require__.e(41).then(() => (() => (__webpack_require__(4041))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react",
/******/ 			},
/******/ 			6438: {
/******/ 				getter: () => (__webpack_require__.e(873).then(() => (() => (__webpack_require__(5873))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react-dom/client",
/******/ 			},
/******/ 			7233: {
/******/ 				getter: () => (__webpack_require__.e(144).then(() => (() => (__webpack_require__(3144))))),
/******/ 				shareInfo: {
/******/ 					shareConfig: {
/******/ 					  "fixedDependencies": false,
/******/ 					  "requiredVersion": "^18.0.0",
/******/ 					  "strictVersion": false,
/******/ 					  "singleton": true,
/******/ 					  "eager": false
/******/ 					},
/******/ 					scope: ["default"],
/******/ 				},
/******/ 				shareKey: "react-dom",
/******/ 			}
/******/ 		};
/******/ 		// no consumes in initial chunks
/******/ 		var chunkMapping = {
/******/ 			"85": [
/******/ 				3355
/******/ 			],
/******/ 			"144": [
/******/ 				3355
/******/ 			],
/******/ 			"842": [
/******/ 				6300,
/******/ 				3355,
/******/ 				6438
/******/ 			],
/******/ 			"873": [
/******/ 				7233
/******/ 			]
/******/ 		};
/******/ 		__webpack_require__.f.consumes = (chunkId, promises) => {
/******/ 			__webpack_require__.federation.bundlerRuntime.consumes({
/******/ 			chunkMapping: chunkMapping,
/******/ 			installedModules: installedModules,
/******/ 			chunkId: chunkId,
/******/ 			moduleToHandlerMapping: moduleToHandlerMapping,
/******/ 			promises: promises,
/******/ 			webpackRequire:__webpack_require__
/******/ 			});
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		__webpack_require__.b = document.baseURI || self.location.href;
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			792: 0
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.f.j = (chunkId, promises) => {
/******/ 				// JSONP chunk loading for javascript
/******/ 				var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
/******/ 				if(installedChunkData !== 0) { // 0 means "already installed".
/******/ 		
/******/ 					// a Promise means "currently loading".
/******/ 					if(installedChunkData) {
/******/ 						promises.push(installedChunkData[2]);
/******/ 					} else {
/******/ 						if(true) { // all chunks have JS
/******/ 							// setup Promise in chunk cache
/******/ 							var promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));
/******/ 							promises.push(installedChunkData[2] = promise);
/******/ 		
/******/ 							// start chunk loading
/******/ 							var url = __webpack_require__.p + __webpack_require__.u(chunkId);
/******/ 							// create error before stack unwound to get useful stacktrace later
/******/ 							var error = new Error();
/******/ 							var loadingEnded = (event) => {
/******/ 								if(__webpack_require__.o(installedChunks, chunkId)) {
/******/ 									installedChunkData = installedChunks[chunkId];
/******/ 									if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
/******/ 									if(installedChunkData) {
/******/ 										var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 										var realSrc = event && event.target && event.target.src;
/******/ 										error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
/******/ 										error.name = 'ChunkLoadError';
/******/ 										error.type = errorType;
/******/ 										error.request = realSrc;
/******/ 										installedChunkData[1](error);
/******/ 									}
/******/ 								}
/******/ 							};
/******/ 							__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 		};
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		// no on chunks loaded
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 		
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunk_crm_if_party_master"] = self["webpackChunk_crm_if_party_master"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// module cache are used so entry inlining is disabled
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	__webpack_require__(3070);
/******/ 	var __webpack_exports__ = __webpack_require__(4639);
/******/ 	
/******/ })()
;